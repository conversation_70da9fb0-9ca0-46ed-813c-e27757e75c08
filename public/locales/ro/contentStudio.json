{"title": "Studio de Conținut", "searchPlaceholder": "Caut<PERSON> imagini...", "loading": "Se încarcă...", "cannotDeleteNonEmptyFolder": "Nu se poate șterge un dosar care nu este gol. Vă rugăm să mutați sau să ștergeți conținutul acestuia mai întâi.", "confirmDeleteTitle": "Con<PERSON><PERSON><PERSON>", "confirmDeleteMessage": "Ești sigur că vrei să ștergi imaginile selectate? Această acțiune nu poate fi anulată.", "confirmDeleteSingleImageTitle": "Con<PERSON><PERSON><PERSON>", "confirmDeleteSingleImageMessage": "Ești sigur că vrei să ștergi această imagine? Această acțiune nu poate fi anulată.", "confirmDeleteFolderTitle": "<PERSON><PERSON><PERSON><PERSON>", "confirmDeleteFolderMessage": "Ești sigur că vrei să ștergi dosarul \"{{folderName}}\"? Această acțiune nu poate fi anulată.", "delete": "Șterge", "deleteFolder": "<PERSON><PERSON><PERSON> dosar", "useInProject": "Folosește în proiect", "uploadImages": "Încarcă imagini", "moveToFolder": "Mută în dosar", "addTags": "Adaugă etichete", "clearSelection": "Șterge selecția", "sort.newest": "<PERSON>le mai noi", "sort.oldest": "<PERSON><PERSON> mai vechi", "sort.name-asc": "Nume (A-Z)", "sort.name-desc": "Nume (Z-A)", "sort.size-asc": "Dimensiune (Mică)", "sort.size-desc": "<PERSON><PERSON><PERSON><PERSON> (Mare)", "view.grid": "Vizualizare G<PERSON>", "view.list": "Vizualizare <PERSON>", "filters": "Filtre", "applyFilters": "Aplică filtre", "noImages": "Nu s-au găsit imagini.", "noFolders": "Nu există dosare disponibile.", "createFolder": "Creează dosar", "renameFolder": "Redenumește dosar", "confirm": "Confirmă", "cancel": "Anulează", "close": "<PERSON><PERSON><PERSON>", "folderSearchPlaceholder": "Caut<PERSON> dosare...", "foldersLabel": "<PERSON><PERSON><PERSON>", "searchResults": "Rezultatele căutării", "noFoldersFound": "Nu s-au gă<PERSON>t dosare", "allFiles": "<PERSON><PERSON>", "myFolders": "<PERSON><PERSON><PERSON>le", "createSubfolder": "Creează subdosar", "folderNames": {"all": "<PERSON><PERSON>", "recent": "<PERSON><PERSON>", "favorites": "Favorite"}, "sort": {"label": "Sortare", "newest": "<PERSON>le mai noi", "oldest": "<PERSON><PERSON> mai vechi", "nameAsc": "Nume (A-Z)", "nameDesc": "Nume (Z-A)", "sizeAsc": "Dimensiune (Mică)", "sizeDesc": "<PERSON><PERSON><PERSON><PERSON> (Mare)"}, "noImagesFound": "Nu s-au găsit imagini", "noImagesHelp": "Încarcă imagini sau selectează un dosar diferit", "viewDetails": "<PERSON><PERSON><PERSON> de<PERSON>", "view": "Vizualizare", "tableHeader": {"name": "Nume", "size": "<PERSON><PERSON><PERSON><PERSON>", "type": "Tip", "date": "Data"}, "imageDetails": {"title": "<PERSON><PERSON><PERSON> imagine", "videoTitle": "Detalii Video"}, "videoUrl": "URL Video", "edit": "Editează", "save": "Salvează", "tags": "Etichete", "add": "Adaugă", "addTagPlaceholder": "Adaugă o etichetă...", "uploaded": "Încărcat:", "fileInfo": "Informații fișier", "type": "Tip", "size": "<PERSON><PERSON><PERSON><PERSON>", "dimensions": "<PERSON><PERSON><PERSON><PERSON>", "location": "Locație", "imageUrl": "URL imagine", "urlCopied": "URL copiat!", "noTags": "<PERSON><PERSON><PERSON><PERSON> etiche<PERSON>", "move": "<PERSON><PERSON><PERSON>", "deleteImageTitle": "<PERSON><PERSON><PERSON>a", "deleteImageMessage": "Ești sigur că vrei să ștergi această imagine? Această acțiune nu poate fi anulată.", "deleteImageConfirm": "<PERSON><PERSON><PERSON>a", "moveDialog": {"title": "Mutare elemente", "selectDestination": "Selectează dosarul de destinație:", "rootOption": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON>)", "noFolders": "Nu există alte dosare disponibile", "moveButton": "<PERSON><PERSON><PERSON>"}, "editConfirm": {"title": "Salvează modificările", "message": "Ești sigur că vrei să salvezi modificările acestui element? Acest lucru va actualiza metadatele în biblioteca ta.", "confirm": "Salvează modificările"}, "clearFilters": "<PERSON><PERSON>ge filtrele", "dateRange": "Interval de date", "dateFrom": "De <PERSON>", "dateTo": "Până la", "searchTagsPlaceholder": "Caută etichete...", "noTagsMatchSearch": "<PERSON><PERSON> etiche<PERSON> nu corespunde căutării", "noTagsAvailable": "Nu există etichete disponibile", "selected": "selectat", "uploadFiles": "Încar<PERSON><PERSON>", "uploading": "Se încarcă...", "dropFilesHere": "Trage fișiere aici", "dragDropFilesHere": "Trage și plasează fișiere aici", "releaseToUpload": "Eliberează pentru a încărca fișierele", "dragDropOrBrowse": "Trage și plasează fișiere imagine aici sau apasă butonul de mai jos pentru a naviga", "browseFiles": "Răsfoiește fișiere", "fileTitle": "<PERSON><PERSON><PERSON>", "tagsForFile": "Etichete pentru", "pressEnterToAddTag": "Apasă Enter pentru a adăuga fiecare etichetă", "folderLocation": "Loca<PERSON><PERSON>", "rootAllFiles": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON>)", "noFilesSelected": "<PERSON><PERSON><PERSON> selectat", "selectFilesHint": "Selectează fișiere trăgându-le în zona de încărcare sau folosind butonul de răsfoire", "supportedFormats": "Formate acceptate: JPG, PNG, GIF, WebP, HEIC, MP4, MOV", "maxFileSize": "Dimensiune maximă fișier: 5MB", "filesToUpload": "Fișiere de încărcat", "addMore": "Adaug<PERSON> mai multe", "selectFilesToUpload": "Selectează fișiere pentru a încărca", "upload": "Încar<PERSON><PERSON>", "errorMaxFiles": "Poți selecta până la {{count}} fișiere.", "errorFileSizeExceeded": "\"{{fileName}}\" depășește dimensiunea maximă acceptată. Se va omite...", "errorNoValidFiles": "Nu există fișiere valide pentru a fi încărcate.", "errorMissingFileTitle": "Toate fișierele trebuie să aibă un nume.", "errorFileTypeNotSupported": "Tipul de fișier al {{fileName}} nu este acceptat. Formate acceptate: JPG, PNG, GIF, WebP, HEIC, MP4, MOV.", "uploadError": "A apărut o eroare în timpul încărcării.", "placeholderAddTag": "Adaugă o etichetă și apasă Enter", "enterTagsForSingle": "Introdu etichete pentru 1 element", "enterTagsForMultiple": "Introdu etichete pentru mai multe elemente", "addAnotherTagPlaceholder": "Adaugă altă etichetă...", "commonlyUsedTags": "Etichete utilizate frecvent", "addTagsButton": "Adaugă etichete", "suggestedTags": {"app": "Aplicație", "code": "Cod", "coding": "Programare", "coffee": "Cafea", "css": "CSS", "design": "Design", "development": "Dezvoltare", "html": "HTML", "javascript": "JavaScript", "laptop": "Laptop", "mobile": "Mobil", "programming": "Programare", "react": "React", "screen": "Ecran", "web": "Web", "webdev": "Dezvoltare Web", "workspace": "Spațiu de lucru"}, "createNewFolder": "Creează dosar nou", "folderName": "Nume dosar", "placeholderFolderName": "Introdu numele dosarului", "parentFolder": "<PERSON><PERSON>", "errorFolderNameRequired": "Numele dosarului este obligatoriu", "errorFolderAlreadyExists": "Există deja un dosar cu acest nume", "newFolderName": "Nume dosar nou", "placeholderNewFolderName": "Introdu numele noului dosar", "errorFileMustHaveName": "Fișierul trebuie să aibă un nume.", "chooseAnImage": "Alege o imagine", "uploadSomeImages": "Încarcă niște imagini sau selectează un alt folder", "makeBielaAwareOfImages": "Fă-o pe Biela conștientă de ce este în imagini", "copiedImage": "Imaginea a fost copiată!", "copiedImageError": "A apărut o eroare la copiere"}