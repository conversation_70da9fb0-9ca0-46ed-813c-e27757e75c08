{"whatWouldYouLikeToBuild": "Transforma-ti ideea intr-un site sau aplicatie live in cateva minute", "whatWouldYouLikeToBuildSubtitle": "Daca iti poti <1>imagina</1>, il poti <5>programa</5>.", "fromIdeaToDeployment": "Începe gratuit. Programează orice. Transformă-ți abilitățile în oportunități cu fiecare prompt.", "codePlaceholder": "Daca iti poti imagina, BIELA poate sa o codeze, ce vom face astazi?", "defaultPlaceholder": "Cum te pot ajuta astazi? Hai sa facem ceva extraordinar impreuna", "checkingFeatures": "Se verifica functionalitatile", "checklists": "Liste de verificare", "runUnitTestsSuggestionTitle": "Sugestie", "runUnitTestsSuggestionMessage": "Doresti sa <PERSON>zi teste unitare pentru proiectul tau?", "runUnitTestsPrimaryButton": "Ruleaza testele unitare", "runUnitTestsSecondaryButton": "<PERSON><PERSON>", "createDatabaseTitle": "<PERSON><PERSON><PERSON> bazei de date", "createDatabaseMessage": "<PERSON>resti sa creezi o baza de date pentru proiectul tau?", "createDatabasePrimaryButton": "Creeaza baza de date", "createDatabaseSecondaryButton": "<PERSON><PERSON>", "extendedThinking": "Gandire extinsa", "extendedThinkingTooltip": "Permite AI să gandeasca mai profund inainte de a raspunde", "extendedThinkingTooltipDisabled": "Dezactiveaza gandirea extinsa pentru a economisi resurse", "firstResponseOnly": "Doar primul răspuns", "always": "Intotdeauna", "AIReasoning": "Rationament AI", "thinking": "Gandire", "attachFile": "Atasează fisier", "voiceInput": "Intrare vocala", "selectLanguage": "Selecteaza limba pentru intrarea vocala", "languageSelectionDisabled": "Selectarea limbii este dezactivata in timpul inregistrarii", "notAvailableInFirefox": "Această funcție nu este disponibilă în Firefox", "enhancePrompt": "Imbunatateste promptul", "cleanUpProject": "Curata proiectul", "clearChatHistory": "Șterge istoricul conversației", "ChatNotFound": "Nu s-a gă<PERSON>t niciun chat sau utilizator", "ChatClearFailedServer": "Ștergerea istoricului conversației a eșuat (eroare server).", "NoMessagesToClear": "Nu există mesaje de șters", "ChatClearSuccess": "Istoricul conversației a fost șters!", "ChatClearFailedUnexpected": "Ștergerea istoricului conversației a eșuat (eroare neașteptată).", "ClearChatTitle": "Șterge istoricul conversației", "ClearChatConfirm": "Ești sigur că vrei să ștergi istoricul conversației?", "ClearChatIrreversible": "Această acțiune nu poate fi anulată.", "ClearChatPro": "Eliberează tokeni pentru performanță mai bună.", "ClearChatCon": "AI-ul nu va mai reține mesajele anterioare, dar va avea în continuare acces la cod.", "Advantage": "<PERSON><PERSON><PERSON>", "Disadvantage": "<PERSON><PERSON><PERSON><PERSON>", "showPrompt": "<PERSON>ta sugestia", "hidePrompt": "Ascunde sugestia", "sendButton": "Trimite", "abortButton": "Anuleaza", "inspirationTitle": "Ai nevoie de inspiratie? Încearcă una dintre acestea:", "cleanUpPrompt": "Curata proiectul asigurandu-te ca niciun fisier nu depaseste 300 de linii de cod. Refactorizeaza fisierele mari in componente modulare mai mici, mentinand functionalitatea completa. Elimina toate fisierele, codul, componentele si datele redundante care nu mai sunt necesare. Asigura-te ca toate componentele raman corect conectate si functionale, pentru a evita orice intrerupere a sistemului existent. Mentine integritatea codului verificand ca nicio modificare nu introduce erori sau nu afecteaza functionalitatile curente. Scopul este de a optimiza proiectul din punct de vedere al eficientei, mentenabilitatii si claritatii.", "checklistPrompt": "Examineaza sugestia mea initiala, intelege obiectivul pas cu pas si creeaza-mi o lista de verificare cu o bifa verde pentru tot ce a fost realizat si cu o bifa rosie pentru ce mai trebuie facut.", "personalPortfolioIdea": "Creeaza un site web de portofoliu personal cu tema intunecata", "recipeFinderIdea": "Construieste o aplicatie de cautare a retetelor care sugereaza mese pe baza ingredientelor", "weatherDashboardIdea": "Proiecteaza un panou meteo cu fundaluri animate", "habitTrackerIdea": "Dezvolta un tracker de obiceiuri cu vizualizare a progresului", "loading": "Se încarcă", "checkingYourAuthenticity": "Verificăm autenticitatea ta", "error": "Eroare", "succes": "Succes!", "tryAgain": "Încearcă din nou", "dashboard": "<PERSON><PERSON>", "getStartedTitle": "Incepe", "getStartedSub": "Descopera cum functioneaza Biela.dev", "createProject": "Creeaza un proiect nou", "createProjectSub": "Porneste de la zero", "editProjectName": "Editează numele proiectului", "editName": "Editează numele", "uploadProject": "Incarca proiectul", "uploadProjectSub": "Importa un proiect existent", "importChat": "Importa chatul", "importChatSub": "Importa un chat existent", "createFolder": "<PERSON><PERSON>aza un dosar nou", "createFolderSub": "Organizeaza-ti proiectele", "cancel": "Anuleaza", "changeFolder": "Sc<PERSON><PERSON> dos<PERSON>l", "save": "Salveaza", "importing": "Se importa...", "importFolder": "Importa dosarul", "giveTitle": "Da un titlu", "projects": "Proiecte", "searchProjects": "<PERSON>auta proiecte...", "becomeAffiliate": "<PERSON><PERSON>", "exclusiveGrowth": "Beneficii exclusive de crestere", "lifetimeEarnings": "Venituri pe viata", "highCommissions": "Comisioane mari", "earnCommission": "Castiga 50% comision la prima ta vanzare", "joinAffiliateProgram": "Alatura-te programului nostru de afiliere", "folders": "<PERSON><PERSON><PERSON>", "organizeProjects": "Organizeaza-ti proiectele pe categorii", "createNewFolder": "<PERSON><PERSON>aza un dosar nou", "enterFolderName": "Introdu numele dosarului", "editFolder": "<PERSON><PERSON><PERSON>", "deleteFolder": "<PERSON><PERSON><PERSON>", "all": "Toate", "webProjects": "Proiecte web", "mobilepps": "Aplicatii mobile", "developmentComparison": "Compararea dezvoltarii", "traditionalVsAI": "Traditional vs IA", "traditional": {"subtitle": "Abordare Standard", "devCost": "Cost de Dezvoltare", "devTime": "<PERSON><PERSON>", "teamExpertise": "Echipă & Expertiză", "teamComposition": "Componența Echipei", "developerLevel": "Nivelul Dezvoltatorului", "hoursBreakdown": "Defalcare Ore", "totalDevTime": "Timp Total de Dezvoltare", "days": "zile", "timeToMarket": "Timp până la Lansare pe Piață", "maintenance": "Mentenanță"}, "standardApproach": "Abordare standard", "developmentCost": "Costul dezvoltarii", "developmentTime": "<PERSON><PERSON>", "costSavings": "Economii de costuri", "reducedCosts": "<PERSON><PERSON><PERSON> reduse", "timeSaved": "<PERSON><PERSON> economisit", "fasterDelivery": "Livrare mai rapida", "bielaDevAI": "Biela.dev IA", "nextGenDevelopment": "Dezvoltare de noua generatie", "developmentCosts": "Costuri de dezvoltare", "openInGitHub": "Deschide pe GitHub", "downloadProject": "Descarca proiectul", "duplicateProject": "Duplicare proiect", "openProject": "Deschide proiectul", "deleteProject": "Sterge proiectul", "confirmDelete": "Sterge acest dosar?", "invoicePreview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": {"title": "<PERSON><PERSON>", "deployment": {"AdvancedSettings": {"advanced-settings": "Set<PERSON> a<PERSON>", "configure-advanced-deployment-options": "Configureaza optiuni avansate de deployment", "server-configuration": "Configurarea serverului", "memory-limit": "<PERSON><PERSON> de memorie", "region": "Regiune", "security-settings": "Setari de securitate", "enable-ddos-protection": "Activeaza protectia DDoS", "protect-against-distributed": "Protejeaza impotriva atacurilor distribuite de tip denial-of-service", "ip-whitelisting": "Lista alba IP", "restrict-acces-to": "Restrictioneaza accesul la anumite adrese IP", "deployment-options": "Optiuni de deployment", "auto-deploy": "Deployment automat", "automatically-deploy-when": "Deploy automat atunci cand se face push pe ramura principala", "preview-deployments": "Previzualizeaza deployment-urile", "create-preview-deployments": "Creeaza deployment-uri de previzu<PERSON><PERSON>e pentru pull request-uri"}, "BuildSettings": {"build-and-deployment-settings": "Setari de build si deployment", "build-command": "Comanda de <PERSON>", "override": "<PERSON>pras<PERSON><PERSON>", "output-directory": "Director de i<PERSON>re", "override2": "<PERSON>pras<PERSON><PERSON>"}, "DatabaseConfiguration": {"database-configuration": "Configurarea bazei de date", "configure-your-db-connections": "Configureaza conexiunile si setarile bazei de date", "database-type": "Tipul bazei de date", "connection-string": "<PERSON> <PERSON>", "your-db-credentials": "Credentialele tale pentru baza de date sunt criptate si stocate in siguranta", "database-settings": "<PERSON><PERSON> pentru baza de date", "pool-size": "Dimen<PERSON>unea pool-ului", "require": "Necesita", "prefer": "Prefera", "disable": "Dezactiveaza", "add-database": "Adauga baza de date"}, "DomainSettings": {"domain-settings": "Setari de domeniu", "configure-your-custom-domain": "Configureaza domeniile personalizate si certificatele SSL", "custom-domain": "Domeniu personalizat", "add": "Adauga", "ssl-certificate": "Certificat SSL", "auto-renew-ssl-certificates": "Reinnoieste automat certificatele SSL", "auto-renew-before-expiry": "Reinnoieste automat certificatele SSL inainte de expirare", "force-https": "Forteaza HTTPS", "redirect-all-http-traffic": "Redirectioneaza tot traficul HTTP catre HTTPS", "active-domain": "Domenii active", "remove": "Elimina"}, "EnvironmentVariables": {"environment-variables": "Variabile de mediu", "configure-environment-variables": "Configureaza variabilele de mediu pentru deployment-uri", "all-enviroments": "Toate mediile", "environment-variables2": "Variabile de mediu", "preview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "development": "Dezvoltare", "create-new": "Creeaza nou", "key": "<PERSON><PERSON><PERSON>", "value": "Valoare", "save-variable": "Salveaza variabila", "you-can-also-import": "De asemenea, poti importa variabile dintr-un fisier .env:", "import-env-file": "Importa fisierul .env"}, "ProjectConfiguration": {"project-configuration": "Configurarea proiectului", "config-your-project-settings": "Configureaza setarile proiectului si optiunile de deployment", "project-url": "URL-ul proiectului", "framework": "Framework", "repo": "Repository", "branch": "Branch", "main": "main", "development": "development", "staging": "staging"}}, "identity": {"unverified": {"title": "Verificarea Identității", "description": "Verificați-vă identitatea pentru a utiliza Biela.dev", "subtitle": "Proces de Verificare Securizat", "processServers": "Toate informațiile cardului sunt stocate în siguranță pe serverele Stripe, nu pe serverele Biela", "processCharge": "Cardul dumneavoastră nu va fi taxat fără consimțământul dumneavoastră explicit pentru un abonament", "processBenefits": "Biela.dev este complet gratuit pentru conturile verificate până pe 15 mai 2025", "verifyStripe": "Verifică cu card de credit sau de debit", "verifyStripeDescription": "Conectați-vă metoda de plată pentru verificare", "verifyNow": "Verificați Acum"}, "verified": {"title": "Identitate Verificată", "description": "Identitatea dumneavoastră a fost verificată cu succes", "paymentMethod": "Metod<PERSON> de Plată", "cardEnding": "Cardul se termină în", "updatePayment": "Actualizați Metoda de Plată", "untilDate": "Până pe 15 mai 2025", "freeAccess": "Acces Gratuit", "freeAccessDescription": "Bucurați-vă de acces complet la Biela.dev făr<PERSON> costuri", "secureStorage": "Stocare Securizată", "secureStorageDescription": "Informațiile cardului tău sunt stocate în siguranță pe serverele Stripe, nu pe serverele Biela. Cardul tău nu va fi taxat fără consimțământul tău explicit pentru un abonament.", "subscriptionAvailable": "Abonamentele vor fi disponibile începând cu 15 mai 2025."}, "connectingToStripe": "Conectare la Stripe..."}, "tabs": {"billing": "Facturare", "profile": "Profil", "deployment": "Deployment", "identity": "Identitate"}}, "help": {"title": "Cum te putem ajuta?", "searchPlaceholder": "<PERSON><PERSON><PERSON> in documentatie...", "categories": {"getting-started": {"title": "<PERSON><PERSON><PERSON> pasi", "description": "Invata elementele de baza despre cum se foloseste Biela.dev", "articles": ["Ghid rapid de incepere", "Prezentare generala a platformei", "Crearea primului tau proiect", "Intelegerea dezvoltarii cu IA"]}, "ai-development": {"title": "Dezvoltare cu IA", "description": "Stapaneste dezvoltarea asistata de IA", "articles": ["Scrierea prompturilor eficiente", "<PERSON>le mai bune practici pentru generarea de cod", "Sfaturi pentru depanarea IA", "Functionalitati avansate ale IA"]}, "project-management": {"title": "Managementul proiectelor", "description": "Organizeaza si gestioneaza proiectele tale", "articles": ["Structura proiectului", "Colaborare in echipa", "Controlul versiunilor", "Optiuni de deployment"]}}, "channels": {"docs": {"name": "Documentatie", "description": "Ghiduri complete si referinte API"}, "community": {"name": "Comunitate", "description": "Conecteaza-te cu alti dezvoltatori"}, "github": {"name": "GitHub", "description": "Raporteaza probleme si contribui"}}, "support": {"title": "Inca ai nevoie de ajutor?", "description": "Echipa noastra de suport este disponibila 24/7 pentru a te ajuta cu orice intrebare sau problema ai avea.", "button": "Contacteaza suportul"}}, "getStarted": {"title": "Descopera cum functioneaza Biela.dev", "description": "Construieste-ti aplicatia in cateva minute cu Biela.dev. IA noastra automatizeaza intregul proces de dezvoltare, de la configurare pana la deployment. Iata cum IA noastra iti construieste aplicatia fara efort!", "features": {"docs": {"title": "Documentatie pentru dezvoltatori", "description": "Invata cum sa folosesti Biela.dev cu ghiduri usor de urmat, sfaturi si cele mai bune practici. Perfect atat pentru incepatori, cat si pentru dezvoltatori experimentati!", "cta": "Exploreaza documentatia pentru dezvoltatori"}, "support": {"title": "Feedback si suport", "description": "Ai nevoie de ajutor sau doresti sa ne oferi feedback? Contacteaza suportul si ajuta-ne sa imbunatatim Biela.dev!", "cta": "Trimite feedback"}, "platform": {"title": "Functionalitati ale platformei", "description": "Descopera instrumentele puternice pe care Biela.dev le ofera pentru a te ajuta sa creezi site-uri si aplicatii fara efort. Lasa IA sa se ocupe de codare pentru tine!", "cta": "Exploreaza functionalitatile"}}, "video": {"title": "Video de inceput rapid", "description": "Vezi cum Biela.dev construieste pentru tine – creare de aplicatii si site-uri fara efort!", "cta": "<PERSON><PERSON><PERSON><PERSON> tutorialul"}, "guide": {"title": "<PERSON><PERSON><PERSON> de inceput rapid", "steps": {"setup": {"title": "Configureaza-ti proiectul instantaneu", "description": "Pregateste-ti mediul de dezvoltare in cateva secunde"}, "generate": {"title": "Genereaza cod full-stack cu IA", "description": "Lasa IA sa scrie cod gata de productie pentru tine"}, "features": {"title": "Generare instantanee de functionalitati", "description": "Adauga functionalitati complexe cu prompturi simple"}, "editor": {"title": "Editor no-code & low-code", "description": "Modifica-ti aplicatia vizual sau prin cod"}, "optimize": {"title": "Optimizeaza si testeaza in timp real", "description": "Asigura-te ca aplicatia ta functioneaza perfect"}, "deploy": {"title": "Deploy cu un singur clic", "description": "Pune aplicatia online instantaneu cu deployment automat"}}}, "faq": {"title": "Intrebari frecvente", "questions": {"what": {"question": "Ce este Biela.dev?", "answer": "Biela.dev este o platforma alimentata de IA care te ajuta sa creezi site-uri si aplicatii – chiar daca nu stii sa programezi. Automatizeaza intregul proces de dezvoltare, de la scrierea codului pana la deployment."}, "experience": {"question": "Am nevoie de experienta in programare pentru a folosi Biela.dev?", "answer": "Nu! Biela.dev este conceput atat pentru incepatori, cat si pentru dezvoltatori experimentati. Poti construi cu ajutorul IA sau poti personaliza codul generat dupa cum este necesar."}, "projects": {"question": "Ce tipuri de proiecte pot crea?", "answer": "Poti crea site-uri web, web apps, aplicatii mobile, platforme SaaS, magazine online, panouri administrative si multe altele."}, "edit": {"question": "Pot edita codul generat de Biela.dev?", "answer": "Da! Biela.dev iti permite sa personalizezi codul generat de IA sau sa folosesti editorul no-code/low-code pentru modificari usoare."}, "deployment": {"question": "Cum functioneaza deployment-ul?", "answer": "Cu un singur clic, Biela.dev iti deploy-eaza proiectul, punandu-l online si gata de utilizare. Nu este necesara configurarea manuala a serverului!"}, "pricing": {"question": "Biela.dev este gratuit?", "answer": "Biela.dev ofera un plan gratuit cu functionalitati de baza. Pentru instrumente si resurse mai avansate, poti face upgrade la un plan premium."}, "integrations": {"question": "Pot integra instrumente sau baze de date terte?", "answer": "Da! Biela.dev suporta integrarea cu baze de date populare (MongoDB, Firebase, Supabase) si API-uri terte."}, "help": {"question": "Unde pot obtine ajutor daca intampin probleme?", "answer": "Poti consulta Documentatia pentru dezvoltatori, Ghidul de inceput rapid sau poti contacta Suportul prin centrul nostru de asistenta."}}}, "cta": {"title": "Esti gata sa incepi?", "description": "Creeaza-ti primul proiect cu Biela.dev si experimenteaza viitorul dezvoltarii.", "button": "Creeaza proiect nou"}}, "confirmDeleteProject": "Confirmă ștergerea proiectului", "confirmRemoveFromFolder": "Confirmă eliminarea din dosar", "deleteProjectWarning": "Ești sigur că vrei să ștergi definitiv {{projectName}}?", "removeFromFolderWarning": "<PERSON>ști sigur că vrei să elimini {{projectName}} din {{folderName}}?", "confirm": "Confirmă", "confirmDeleteFolder": "Confirmă ștergerea <PERSON>arului", "deleteFolderWarning": "Ești sigur că vrei să ștergi {{folderName}}? Această acțiune nu poate fi anulată.", "folderDeletedSuccessfully": "<PERSON><PERSON> <PERSON><PERSON> cu succes", "downloadChat": "Descarcă chatul", "inactiveTitle": "Acest tab este inactiv", "inactiveDescription": "Apasă butonul de mai jos pentru a activa acest tab și a continua utilizarea aplicației.", "inactiveButton": "Folosește acest tab", "suggestions": {"weatherDashboard": "Creeaza un panou meteo", "ecommercePlatform": "Construieste o platforma de e-commerce", "socialMediaApp": "Proiecteaza o aplicatie de social media", "portfolioWebsite": "Genereaza un site de portofoliu", "taskManagementApp": "Creeaza o aplicatie de management al sarcinilor", "fitnessTracker": "Construieste un tracker de fitness", "recipeSharingPlatform": "Proiecteaza o platforma pentru partajarea retetelor", "travelBookingSite": "Creeaza un site de rezervari de calatorii", "learningPlatform": "Construieste o platforma de invatare", "musicStreamingApp": "Proiecteaza o aplicatie de streaming muzical", "realEstateListing": "<PERSON><PERSON>az<PERSON> un anunt imobiliar", "jobBoard": "Construieste un site de joburi"}, "pleaseWait": "<PERSON><PERSON> rugăm să așteptați...", "projectsInAll": "Toate proiectele", "viewProjects": "Viz<PERSON><PERSON><PERSON> toate proiectele din foldere", "projectCount": "{{count}} proiect", "projectCount_plural": "{{count}} proiecte", "projectsInCurrentFolder": "Proiecte în {{folderName}}", "createYourFirstProject": "Creați primul dvs. proiect", "startCreateNewProjectDescription": "Începeți să construiți ceva uimitor. Proiectele dvs. vor fi afișate aici odată ce le creați.", "createProjectBtn": "Proiect Nou", "publishedToHackathon": "Publicat la Hackathon", "publishToHackathon": "Publică la Hackathon", "refreshSubmission": "Reîmprospătați trimiterea", "hackathonInformation": "Informații despre <PERSON>", "selectFolder": "Alegeți un dosar", "folder": "Dosar", "selectAFolder": "Selectați un dosar", "thisProject": "acest proiect", "projectDeletedSuccessfully": "Proiect șters cu succes", "projectRemovedFromFolder": "Proiect eliminat din dosar", "unnamedProject": "Proiect fără nume", "permanentDeletion": "<PERSON><PERSON><PERSON><PERSON>", "removeFromFolder": "Elimina<PERSON><PERSON> din {{folderName}}", "verifiedAccount": "Cont verificat", "hasSubmittedAMinimumOfOneProject": "A trimis cel puțin un proiect", "haveAtLeastActiveReferrals": "<PERSON><PERSON><PERSON> cel puțin {{number}} recomand<PERSON><PERSON> active", "GoToAffiliateDashBoard": "Accesați tabloul de bord al afiliaților", "LikeOneProjectThatBelongsToAnotherUser": "Apreciați un proiect care aparține altui utilizator", "GoToContestPage": "Accesați pagina <PERSON>on", "VibeCodingHackathonStatus": "Starea Hackathonului Vibe Coding", "ShowcaseYourBestWork": "Prezentați cea mai bună lucrare a dvs.", "submissions": "<PERSON><PERSON><PERSON>", "qualifyConditions": "Condiții de calificare", "completed": "Finalizat", "collapseQualifyConditions": "Restrângeți condițiile de calificare", "expandQualifyConditions": "Extindeți condițiile de calificare", "basicParticipation": "Participare de bază", "complete": "<PERSON><PERSON><PERSON>,", "incomplete": "Necompletat", "forTop3Places": "Pentru primele 3 locuri", "deleteSubmission": "Ștergeți trimiterea", "view": "Vizualizați", "submitMoreProjectsToIncreaseYourChangesOfWining": "Trimiteți mai multe proiecte pentru a vă crește șansele de câștig!", "removeFromHackathon": "Eliminați din Hackathon?", "removeFromHackathonDescription": "Sunteți sigur că doriți să eliminați <project>{{project}}</project> din Hackathon-ul Vibe Coding? Această acțiune nu poate fi anulată.", "cardVerificationRequired": "Verificarea cardului este necesara", "pleaseVerifyCard": "Vă rugăm să verificați cardul pentru a continua", "unlockFeaturesMessage": "Pentru a debloca accesul complet la toate funcționalitățile platformei, solicităm o verificare rapidă a cardului. Acest proces este complet sigur și asigură o experiență fluidă pe Biela.dev. Nu se vor face taxe pe cardul dumneavoastră în timpul verificării.", "freeVerificationNotice": "<PERSON>ef<PERSON><PERSON>", "accessToAllFeatures": "Acces complet la funcționalitățile Biela.dev.", "enhancedFunctionality": "Performanță îmbunătățită a platformei.", "quickSecureVerification": "Proces de verificare sigur și criptat.", "noCharges": "*Fără taxe sau costuri ascunse", "verificationOnly": " — doar verificare.", "verifyNow": "Verifică acum", "DropFilesToUpload": "Trage fișierele pentru a le încărca", "projectInfo": {"information": "Informații despre proiect", "type": "Tipul proiectului", "complexity": "Complexitate", "components": "Componente", "features": "Funcționalități", "confidenceScore": "<PERSON><PERSON>", "estimationUncertainty": "Incetitudinea estimării", "keyTechnologies": "Tehnologii cheie"}, "projectMetrics": {"subtitle": "Dezvoltare de Generație Următoare", "teamExpertise": "Echipă & Expertiză", "developerLevel": "Nivelul Dezvoltatorului", "aiPowered": "Susținut de AI", "nextGenAI": "AI de Generație Următoare", "keyBenefits": "<PERSON><PERSON><PERSON><PERSON>", "descriptionTitle": "Descriere Scurtă a Proiectului", "description": "Biela.dev AI revoluționează dezvoltarea prin automatizarea sarcinilor complexe, asigurând calitatea codului și accelerând livrarea proiectului. Se integrează perfect în fluxurile de lucru, oferind asistență inteligentă de la concept la implementare.", "timeToMarket": "Timp până la Lansare pe Piață", "timeToMarketValue": "6 minute", "uncertainty": "Nesiguranță", "uncertaintyValue": "0% risc", "benefits": {"0": "Dezvoltare Instantanee", "1": "Fără Costuri de Întreținere", "2": "Înalt<PERSON>", "3": "Cod Gata de Producție"}}, "loadingMessages": {"publish": ["Obținerea informațiilor despre proiect...", "Realizarea unei capturi de ecran...", "Generarea unui rezumat și descriere..."], "refresh": ["Recuperarea datelor curente de depunere...", "Actualizarea capturii de ecran a proiectului...", "Reîmprospătarea detaliilor proiectului..."]}, "errorMessages": {"publish": "Nu s-a putut publica \"{{projectName}}\" la hackathon. A apărut o problemă la procesarea cererii tale.", "refresh": "Nu s-a putut actualiza trimiterea \"{{projectName}}\". Serverul nu a putut actualiza informațiile proiectului tău."}, "actions": {"refresh": {"title": "Actualizare trimitere", "successMessage": "Trimiterea proiectului tău a fost actualizată cu succes.", "buttonText": "Vezi trimiterea actualizată"}, "publish": {"title": "Publicare la hackathon", "successMessage": "Proiectul tău a fost trimis la hackathon.", "buttonText": "Vezi pe pagina hackathon-ului"}}, "SupabaseConnect": "Supabase", "SupabaseDashboard": "Supabase", "RestoreApp": "Restaurează", "SaveApp": "Salvează", "ForkChat": "Bifurcă", "BielaTerminal": "Terminal Biela", "UnitTesting": "Testare unitară", "InstallDependencies": "Instalează dependențe", "InstallDependenciesDescription": "Instalează toate pachetele necesare pentru proiect folosind", "BuildProject": "Construiește proiectul", "BuildProjectDescription": "Compilează și optimizează proiectul pentru producție folosind", "StartDevelopment": "Pornește dezvoltarea", "StartDevelopmentDescription": "Pornește serverul de dezvoltare pentru previzualizare live folosind", "NoProjectFound": "Nu a fost găsit niciun proiect cu acel nume.", "NoProjectFoundDescription": " Verifică dacă sunt greșeli de tastare și încearcă din nou.", "ContextLimitReached": "<PERSON>ita de <PERSON> atinsa", "ClaudeContextDescription1": "Ai atins capacitatea de context pentru acest proiect cu Claude. Nu-ti face g<PERSON><PERSON> — putem continua schimband la un model Gemini cu o limita de context semnificativ mai mare.", "ClaudeContextDescription2": "Modelele Gemini ofera de pana la 5 ori mai multa capacitate de context, permitandu-ti sa pastrezi tot codul, istoricul conversatiilor si sa continui dezvoltarea proiectului fara intrerupere.", "SelectModelToContinue": "Selecteaza un model pentru a continua:", "Performance": "Performanta", "UpgradePlan": "un plan premium", "PremiumFeatureRequired": "Functionalitate premium necesara", "LargeContextUpgradeInfo": "Modelele cu fereastra de context extinsa necesita un abonament de nivel superior. Faceti upgrade pentru a le debloca.", "PremiumModelsAvailable": "Modele premium disponibile cu upgrade:", "UpgradeTooltip": "Deblocați prin upgrade la ", "UpgradeTooltipSuffix": "pachet", "UpgradeToContinue": "Faceti upgrade pentru a continua", "PremiumBadge": "Premium", "AlternativeLimitExplanation": "Se pare ca AI-ul a atins limita de procesare pentru acest proiect. Majoritatea spatiului este utilizat de fisierele importate, nu de conversatie.", "SuggestedSolutions": "Solutii sugerate:", "ReimportProject": "Reimporta ca proiect nou", "ReimportProjectDescription": "Aceasta va sterge istoricul conversatiei si va elibera spatiu de context, pastrand in acelasi timp fisierele tale.", "BreakIntoProjects": "Imparte in mai multe proiecte", "BreakIntoProjectsDescription": "Imparte munca in componente mai mici care pot fi dezvoltate separat.", "ExportWork": "Exporta lucrul finalizat", "ExportWorkDescription": "Descarca si arhiveaza fisierele finalizate pentru a elibera spatiu de context.", "AlternativeContextNote": "Pentru a profita la maximum de contextul disponibil, ia în considerare eliminarea fisierelor sau bibliotecilor neutilizate si concentreaza-te pe fisierele esentiale necesare pentru faza actuala de dezvoltare.", "ContinueWithSelectedModel": "Continua cu modelul selectat", "Close": "Inchide", "AIModel": "Model AI", "Active": "Activ", "Stats": "Statistici", "Cost": "Cost", "ExtendedThinkingDisabledForModel": "Indisponibil pentru acest model", "ExtendedThinkingAlwaysOn": "Intotdeauna activ cu acest model", "limitReached": "Ai atins limita!", "deleteProjectsSupabase": "Te rugăm să ștergi câteva proiecte sau să-ți mărești limita în Supabase.", "goTo": "Du<PERSON>te la", "clickProjectSettings": " fă clic pe proiect, <PERSON><PERSON><PERSON> proiect, der<PERSON>az<PERSON> în jos și fă clic", "delete": "Șterge", "retrying": "Se reîncearcă…", "retryConnection": "Reîncearcă cone<PERSON>a", "RegisterPageTitle": "Înregistrare – biela.dev", "RegisterPageDescription": "Creează-ți un cont pe biela.dev și deblochează toate funcționalitățile.", "SignUpHeading": "Înregistrare", "AlreadyLoggedInRedirectHome": "Ești deja autentificat! Redirecționare către pagina principală...", "PasswordsMismatch": "Parolele nu se potrivesc.", "FirstNameRequired": "Prenumele este obligatoriu", "LastNameRequired": "Numele este obligatoriu", "UsernameRequired": "Numele de utilizator este obligatoriu", "EmailRequired": "Adresa de e-mail este obligatorie", "EmailInvalid": "Te rugăm să introduci o adresă de e-mail validă", "TooManyRequests": "Prea multe cereri, te rugăm să încerci mai târziu", "SomethingWentWrongMessage": "Ceva nu a funcționat corect, te rugăm să încerci mai târziu", "PasswordRequired": "Parola este obligatorie", "ConfirmPasswordRequired": "Te rugăm s<PERSON>i parola", "AcceptTermsRequired": "Trebuie să accepți Termenii de utilizare și Politica de confidențialitate", "CaptchaRequired": "Te rugăm s<PERSON>zi CAPTCHA-ul", "RegistrationFailed": "Înregistrarea a eșuat", "EmailConfirmationSent": "Un e-mail de confirmare a fost trimis! Te rugăm să confirmi adresa și apoi să te autentifici.", "RegistrationServerError": "Înregistrarea a eșuat (serverul a returnat false).", "SomethingWentWrong": "Ceva nu a funcționat corect", "CheckEmailHeading": "Verifică e-mailul pentru a confirma înregistrarea", "CheckEmailDescription": "Ți-am trimis un e-mail cu un link de confirmare.", "GoToHomepage": "Mergi la pagina principală", "ReferralCodeOptional": "Cod de recomandare (opțional)", "EnterReferralCode": "Introdu un cod de recomandare dacă ai fost invitat de un alt utilizator.", "PasswordPlaceholder": "Pa<PERSON><PERSON>", "ConfirmPasswordPlaceholder": "Confirmă parola", "CreateAccount": "Creează cont", "AlreadyHaveAccountPrompt": "Ai deja un cont?", "Login": "Autentificare", "AcceptTermsPrefix": "Sunt de acord cu", "TermsOfService": "Termenii de utilizare", "AndSeparator": "și", "PrivacyPolicy": "Politica de confidențialitate", "LoginPageTitle": "Autentificare – biela.dev", "LoginPageDescription": "Accesează-ți contul sau autentifică-te pe biela.dev pentru a folosi toate funcțiile.", "LogInHeading": "Autentificare", "EmailOrUsernamePlaceholder": "E-mail / Nume de utilizator", "ForgotPassword?": "Ai uitat parola?", "LoginToProfile": "Autentifică-te în profilul tău", "UserNotConfirmed": "Contul nu este confirmat", "ConfirmEmailNotice": "Trebuie să-ți confirmi adresa de e-mail pentru a activa contul.", "ResendConfirmationEmail": "Retrimite e-mailul de confirmare", "ResendConfirmationSuccess": "E-mailul de confirmare a fost retrimis! Verifică inboxul.", "ResendConfirmationError": "Trimiterea eșuată a e-mailului de confirmare.", "LoginSuccess": "Autentificare reușită! Redirecționare...", "LoginFailed": "Autentificare eșuată", "LoginWithGoogle": "Autentificare cu Google", "LoginWithPaypal": "Autentificare cu Paypal", "LoginWithGitHub": "Autentificare cu GitHub", "SignUpWithGoogle": "Înscrie-te cu Google", "SignUpWithGitHub": "Înscrie-te cu GitHub", "Or": "SAU", "NoAccountPrompt": "Nu ai un cont?", "SignUp": "Înregistrează-mă", "ForgotPasswordPageTitle": "Parolă uitată – biela.dev", "ForgotPasswordPageDescription": "Resetează parola contului tău biela.dev și recuperează accesul.", "BackToLogin": "Înapoi la autentificare", "ForgotPasswordHeading": "Parolă uitată", "ForgotPasswordDescription": "Introdu adresa ta de e-mail și îți vom trimite un link de verificare pentru resetarea parolei.", "VerificationLinkSent": "Linkul de verificare a fost trimis! Verifică-ți e-mailul.", "EnterYourEmailPlaceholder": "Introdu adresa ta de e-mail", "Sending": "Se trimite...", "SendVerificationCode": "Trimite codul de verificare", "SendVerificationLink": "Trimite linkul de verificare", "InvalidConfirmationLink": "<PERSON> de confirmare invalid", "Back": "Înapoi", "ResetPassword": "Resetează parola", "ResetPasswordDescription": "Creează o nouă parolă pentru contul tău", "NewPasswordPlaceholder": "Parolă nou<PERSON>", "ConfirmNewPasswordPlaceholder": "Confirmă parola nouă", "ResetPasswordButton": "Resetează parola", "PasswordRequirements": "Parola trebuie să conțină cel puțin 8 caractere, o literă mare, o literă mică, un număr și un caracter special.", "PasswordUpdatedSuccess": "Parola a fost actualizată cu succes!", "affiliateDashboard": "<PERSON><PERSON>", "userDashboard": "Panou utilizator", "returnToAffiliateDashboard": "Înapoi la panoul de afiliat", "returnToUserDashboard": "Înapoi la panoul de utilizator", "myProfile": "Profilul meu", "viewAndEditYourProfile": "Vizualizează și editează profilul tău", "billing": "Facturare", "manageYourBillingInformation": "Gestionați informațiile de facturare", "logout": "Deconectare", "logoutDescription": "Deconectați-vă din contul dvs.", "SupabaseNotAvailable": "Supabase nu este disponibil acum, vă rugăm să încercați din nou mai târziu.", "projectActions": {"invalidSlug": "Slug-ul proiectului nu este valid.", "transferLimit": "Ai transferat deja această versiune a proiectului către acest utilizator. Fă modificări pentru a transfera din nou.", "downloadSuccess": "Proiectul a fost descărcat cu succes!", "downloadError": "Descărcarea proiectului a eșuat.", "exportSuccess": "Chatul a fost exportat! Verifică folderul de descărcări.", "exportError": "Exportul chatului a eșuat.", "duplicateSuccess": "Chatul a fost duplicat cu succes!", "duplicateError": "Duplicarea chatului a eșuat."}, "enter_new_phone_number": "Introduceți un număr nou", "enter_new_phone_number_below": "<PERSON>ă rugăm să introduceți noul număr de telefon:", "new_phone_placeholder": "<PERSON><PERSON><PERSON><PERSON> de telefon nou", "enter_otp_code": "Introduceți codul OTP", "confirm_phone_message": "Pentru a folosi contul, trebuie să confirmați numărul. Introduceți codul trimis la ({{phone}}).", "wrong_phone": "<PERSON><PERSON><PERSON><PERSON>?", "resend_sms": "Retrimite SMS", "submit": "Trimite", "tokensAvailable": "tokeni disponibile", "sectionTitle": "Gestionare Domenii", "addDomainButton": "Adaugă Nume de Domeniu", "connectCustomDomainTitle": "Conectează un Nume de Domeniu Personalizat", "disclaimer": "Declinare de responsabilitate:", "disclaimerText": "Pentru a verifica cu succes, trebuie să setați corect toate regulile DNS de mai jos", "domainInputDescription": "Introduceți numele domeniului pe care doriți să-l conectați la acest proiect.", "domainLabel": "Nume de Domeniu", "domainPlaceholder": "example.com", "cancelButton": "Anulează", "continueButton": "Adaugă Nume de Domeniu", "deployingText": "Se publică...", "addingText": "Se adaugă...", "verifyButtonText": "Verifică", "configureDnsTitle": "Configurează Înregistrările DNS", "configureDnsDescription": "Adăugați următoarele înregistrări DNS domeniului dvs. pentru a verifica deținerea și pentru a-l conecta la acest proiect.", "tableHeaderType": "Tip", "tableHeaderName": "Nume", "tableHeaderValue": "Valoare", "note": "Notă:", "noteText": "Modificările DNS pot dura până la 48 de ore pentru a se propaga. Totuși, de obicei au efect în câteva minute sau ore.", "backButton": "Înapoi", "showDnsButton": "Afișează Setările DNS", "hideDnsButton": "Ascunde Setările DNS", "removeButton": "Șterge", "dnsSettingsTitle": "Setări DNS Domeniu", "removeDomainConfirmTitle": "Șterge Nume de Domeniu", "removeConfirmationText": "<PERSON><PERSON><PERSON> <PERSON>riți să ștergeți numele de domeniu ", "importantCleanupTitle": "Curățare DNS Importantă", "cleanupDescription": "După ce eliminați acest domeniu din proiect, nu uitați să eliminați și înregistrările DNS create la configurare. Aceasta ajută la menținerea unei configurații DNS curate și previne conflictele viitoare.", "confirmRemoveButton": "Șterge Nume de Domeniu", "customConfigTitle": "Configu<PERSON><PERSON>", "customConfigDescription": "Conectați propriile domenii la acest proiect. Proiectul va rămâne întotdeauna accesibil prin domeniul implicit Biela, dar domeniile personalizate oferă o experiență de brand profesională pentru utilizatorii dvs.", "defaultLabel": "Implicit", "statusActive": "Activ", "statusPending": "În așteptare", "lastVerifiedText": "Verificat chiar acum", "errorInvalidDomain": "Introduceți un nume de domeniu valid (de ex. example.com)", "errorDuplicateDomain": "Acest nume de domeniu este deja conectat la proiectul dvs.", "errorAddFail": "Nu s-a putut adăuga numele de domeniu.", "successAdd": "Nume de domeniu adăugat cu succes! Domeniul a fost conectat la acest proiect.", "benefitsTitle": "<PERSON><PERSON><PERSON><PERSON>", "benefitSecurityTitle": "Securitate Avansată", "benefitSecurityDesc": "Toate domeniile personalizate sunt securizate automat cu certificate SSL.", "benefitPerformanceTitle": "Performanță Ridicată", "benefitPerformanceDesc": "CDN-ul global asigură încărcarea rapidă a proiectului pentru utilizatorii din întreaga lume.", "benefitBrandingTitle": "Branding Profesional", "benefitBrandingDesc": "Folosiți propriul domeniu pentru o experiență de brand coerentă.", "benefitAnalyticsTitle": "Integrare cu Analitice", "benefitAnalyticsDesc": "Domeniile personalizate funcționează perfect cu platformele de analiză.", "meta": {"index": {"title": "biela.dev | Construire Web & App cu AI – Construire cu prompt-uri", "description": "Transformă ideile tale în site-uri web sau aplicații funcționale cu biela.dev. Folosește prompt-uri de AI pentru a construi cu ușurință produse digitale personalizate."}, "login": {"title": "Autentificare la Contul dvs. biela.dev", "description": "Accesați panoul dvs. biela.dev pentru a gestiona și construi proiectele generate de AI."}, "register": {"title": "Înregistrează-te la biela.dev – Începe <PERSON>stru<PERSON> cu <PERSON>", "description": "Creează contul dvs. biela.dev pentru a începe construirea site-urilor web și aplicațiilor folosind prompt-uri alimentate de AI."}, "dashboard": {"title": "Panoul dvs. de Proiecte – biela.dev", "description": "Gestionați site-urile și aplicațiile construite cu AI, editați proiectele live și urmăriți istoricul construirii – totul într-un loc."}, "profile": {"title": "Profilul tău – biela.dev", "description": "Vizualizează și actualizează detaliile contului tău biela.dev, gestionează preferințele și personalizează experiența ta de dezvoltare AI."}, "billing": {"title": "Facturare – Gestionează-ți planul în siguranță pe biela.dev", "description": "Accesează setările de facturare pentru a-ți gestiona abonamentul, a actualiza metodele de plată și a avea control total asupra planului tău biela.dev."}}, "transferProject": "Partajează o copie", "transferSecurityNoteDescription": "Destinatarul va primi acces complet la o copie a acestui proiect și la toate resursele asociate.", "transferProjectDescription": "Introdu numele de utilizator sau e-mailul persoanei către care dorești să transferi o copie a acestui proiect.", "transferProjectLabel": "Nume utilizator sau email", "transferProjectPlaceholder": "<NAME_EMAIL>", "transferButton": "Transferă", "transferSecurityNote": "Notă de securitate:", "dontHavePermisionToTransfer": "Nu aveți permisiunea de a transfera acest proiect", "transferProjectUserNotFound": "Utilizatorul {{ user }} nu a fost găsit!", "transferErrorOwnAccount": "Nu poți transfera un proiect către propriul tău cont.", "transferError": "Eroare la transferul proiectului", "transferSuccess": "Transfer realizat cu succes către {{ user }}", "enterValidEmailUsername": "Vă rugăm să introduceți un nume de utilizator sau un email", "enterMinValidEmailUsername": "Introduceți un nume de utilizator valid (minim 3 caractere) sau o adresă de email", "youWillStillHaveAccess": "Vei avea în continuare acces la proiectul original", "newChangesWillNotAffect": "Noile modificări nu vor afecta proiectul celuilalt utilizator", "ProjectInformationNotLoaded": "Informațiile proiectului nu au putut fi încărcate.", "projectInfoTitle": "Informații despre Proiect", "generalInformationProject": "Informații generale despre proiectul tău.", "rename": "Redenumește", "lastlySavedAt": "<PERSON><PERSON><PERSON> sal<PERSON>e:", "noSavedAppVersion": "Nu există nicio versiune salvată a aplicației.", "Owner": "Proprietar", "TechStack": "Stack tehnologic", "FeatureCount": "Număr de funcționalități", "UniqueComponentCount": "<PERSON><PERSON><PERSON>r de componente unice", "differencesModeTooltip": "Activeaza formatul compact de diferente in locul rescrierii complete a fisierului", "differencesMode": "Actualizare diferente AI", "inviteCollaborator": {"title": "INVITĂ COLABORATOR", "message": "Ești pe cale să inviți {{email}} să colaboreze la acest proiect.", "action": "Trimite invitația"}, "removeCollaborator": {"title": "ELIMINĂ COLABORATORUL", "message": "Ești pe cale să elimini {{email}} din acest proiect.", "action": "Elimină"}, "database": {"title": "Conexiuni la Baza de Date", "fields": {"created": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON><PERSON>", "region": "Regiune", "version": "Versiune"}, "button": {"connected": "Conectat", "connect": "Conectează", "notConnected": "Neconectat"}}, "status": {"Online": "Online", "Degraded": "Degradat", "Restoring": "Restaurare în curs", "Restored": "Restaurat", "Creating": "Se creează", "Provisioning": "Se pregătește", "Coming Up": "Se pornește", "Deleting": "Se șterge", "Deleted": "<PERSON><PERSON>", "Pausing": "<PERSON> <PERSON>rer<PERSON>", "Paused": "Întrerupt", "Inactive": "Inactiv", "Suspended": "Suspendat", "Resuming": "Se reia", "Updating": "Se actualizează", "Migrating": "Se migrează", "Maintenance": "Mentenanță", "Restarting": "Se repornește", "Backup in progress": "Backup în curs", "Restore in progress": "Restaurare în curs", "Failed": "<PERSON><PERSON><PERSON><PERSON>", "Unknown": "Necunoscut", "Loading...": "Se încarcă...", "Not Found": "Nu a fost găsit", "Error": "Eroare"}, "titleChat": "Chat {{typeCapitalized}}", "titleMessage": "<PERSON>j {{typeCapitalized}}", "dialogDescriptionText": "<PERSON><PERSON> pe cale să {{type}} {{description}}.", "confirmText": "<PERSON>ști sigur că vrei să {{type}} acest chat?", "deleteButton": "Șterge", "deleteInfinitive": "<PERSON><PERSON><PERSON>", "deleteNoun": "<PERSON><PERSON><PERSON><PERSON>", "duplicateButton": "<PERSON><PERSON><PERSON><PERSON>", "duplicateInfinitive": "<PERSON><PERSON><PERSON><PERSON>", "duplicateNoun": "duplicare", "downloadButton": "Des<PERSON><PERSON><PERSON>", "downloadInfinitive": "<PERSON><PERSON><PERSON>", "downloadNoun": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exportButton": "Exportă", "exportInfinitive": "exporți", "exportNoun": "exportare", "forkButton": "Ramifică", "forkInfinitive": "ram<PERSON>i", "forkNoun": "ramificare", "rollbackButton": "<PERSON><PERSON>", "rollbackInfinitive": "revii", "rollbackNoun": "revenire", "saveButton": "Salvează", "saveInfinitive": "salve<PERSON>", "saveNoun": "salvare", "restoreButton": "Restaurează", "restoreInfinitive": "<PERSON><PERSON><PERSON><PERSON>", "restoreNoun": "restaurare", "loadingOverlayChat": "Te rugăm să a<PERSON>i în timp ce {{type}} chat-ul.", "loadingOverlayMessages": "Te rugăm să a<PERSON>tepți în timp ce {{type}} mesajele.", "exportProjectChat": "Exportă chatul proiectului", "downloadProjectFiles": "Descarcă fișierele proiectului", "pleaseSelectCommit": "Te rog selectează un commit pentru restaurare:", "refresh": "Reîmprospătare", "savingApp": "Se salvează...", "previousAppVersion": "o versiune anterioară a aplicatiei", "CreatingDatabase": "Se creează baza de date...", "DatabaseDashboard": "<PERSON><PERSON><PERSON>ord", "ConnectDatabase": "Conectează-te", "DisconnectAccount": "Deconectează contul", "SuccessfullyLinkedDatabase": "Conectare reușită la această bază de date!", "FailedToLinkDatabase": "Conectarea la această bază de date a eșuat!", "DatabaseConnectionOptions": "Opțiuni de conexiune", "LoadingYourSupabaseProjects": "Se încarcă proiectele Supabase...", "NoSupabaseProjectsFound": "Nu s-au găsit proiecte Supabase. Conectează-te mai întâi.", "DatabaseOptions": "Opțiuni", "DisconnectDatabase": "Deconectează baza de date", "OpenSupabaseDashboard": "Deschide tabloul Supabase", "LinkExistingProject": "Leagă de această bază de date", "IncludeDataWhenDuplicating": "Include date la duplicare", "DuplicateProject": "Dup<PERSON><PERSON> proiect", "CreateNewSupabaseDB": "Creează un nou DB Supabase", "UseExistingSupabaseDB": "Folosește DB Supabase  existent", "DisconnectSupabaseDB": "Deconectează Supabase DB", "SuccessfullyDisconnected": "Te-ai deconectat cu succes de la proiect", "WhatNext": "Ce dorești să faci în continuare?", "RemoveSupabaseIntegration": "Elimină tot codul de integrare Supabase", "KeepCodeButDisableSupabase": "Păstrează codul dar dezactivează integrarea Supabase", "ReplaceBackend": "Înlocuiește backend-ul cu altă soluție", "SupabaseActionInProgress_create": "Se creează o nouă bază de date Supabase. Vă rugăm să așteptați...", "SupabaseActionInProgress_link": "Se leagă proiectul la o bază de date Supabase existentă. Vă rugăm să așteptați...", "SupabaseActionInProgress_duplicate": "Se duplică proiectul Supabase selectat. Vă rugăm să așteptați...", "LinkProjectToDatabase": "Ești pe cale să legi proiectul la baza de date", "AnalyzeProject": "Analizează proiectul meu și discută opțiunile de integrare (recomandat)", "DisplaySchema": "Afișea<PERSON><PERSON> doar schema bazei de date", "SharedFrom": "Partajat de", "TokensExhausted": "<PERSON> ră<PERSON> fă<PERSON>i", "TokensExhaustedDescription": "Pentru a continua proiectul, alege o opțiune:", "upgradePlan": "Actualizează planul meu", "buyMoreTokens": "Cumpără mai multe tokeni", "seeScreenshot": "Vezi captura de ecran", "projectSaved": "Proiect salvat", "projectSavedSuccessfully": "Proiect salvat cu succes", "projectSaveFailed": "Eroare la salvarea proiectului", "ConfirmLogout": "Confirmă delogarea", "LogoutCheck": "Ești sigur că vrei să te deconectezi?", "Pricing": "<PERSON><PERSON><PERSON>", "Affiliates": "A<PERSON>lia<PERSON><PERSON>", "HelpCenter": "<PERSON><PERSON><PERSON>", "Privacy": "Confidențialitate", "SignIn": "Autentificare", "GetStarted": "Înregistrează-te", "JoinVibeCodingHackathon": "Participă la Vibe Coding Hackathon", "WhatDoYouWantToBuild": "Ce vrei să construiești?", "ImaginePromptCreate": "Imagine. Prompt. Creează", "StrategicPartners": "Parteneri strategici", "DevelopedBy": "Dezvoltat de", "ToUseBielaDev": "Pentru a folosi Biela.dev, trebuie să te autentifici într-un cont existent sau să creezi unul folosind una dintre opțiunile de mai jos", "Sign in with Google": "Autentificare cu Google", "Sign in with GitHub": "Autentificare cu GitHub", "Sign in with Email and Password": "Autentificare cu email și parolă", "ByUsingBielaDev": "Prin utilizarea Biela.dev ești de acord cu colectarea datelor de utilizare pentru analiză.", "MissingUsernamePasswordOrTurnstile": "Nume de utilizator, parolă sau token Turnstile lipsă", "HowCanBielaHelpYouToday": "Cum te poate ajuta Biela astăzi?", "TabsSection": {"AvailableAfterPromptProcessed": "Disponibil după procesarea solicitării", "History": "Istoric", "Roadmap": "Strategie"}, "Notifications": "Notific<PERSON><PERSON>", "All": "Toate", "Unread": "Necitite", "TeamMembers": "Me<PERSON>ri e<PERSON>", "TeamUpdates": "Actualizări echi<PERSON>ă", "Requests": "<PERSON><PERSON><PERSON>", "MarkAllAsRead": "<PERSON><PERSON>z<PERSON> toate ca citite", "MarkAsRead": "Marchează ca citit", "NoNotifications": "<PERSON><PERSON> notific<PERSON>", "NoNotificationsDescription": "Momentan nu ai nicio notificare.", "JoinedTeam": "s-a alăturat echipei", "TeamReachedMilestone": "Echipa ta a atins o etapă importantă!", "TeamUnlockedBoost": "Echipa ta a deblocat un nou boost!", "NewNotification": "Notificare nouă", "RequestedToJoin": "a cerut să se alăture echipei tale", "InvitedYouToJoin": "te-a invitat să te alături echipei!", "TeamLedBy": "Echipa condusă de", "TeamLeader": "Căpitanul echipei", "Accept": "Acceptă", "Decline": "Refuză", "View": "<PERSON><PERSON><PERSON>", "MembershipRequest": "<PERSON><PERSON><PERSON> de <PERSON>mbru", "TeamInvitation": "Invitație în echipă", "RejectFor30Days": "Refuză cererea pentru următoarele 30 de zile", "Approved": "Aprobat", "Canceled": "<PERSON><PERSON><PERSON>", "Rejected": "Respins", "Stars": "Stele", "Members": "<PERSON><PERSON><PERSON>", "CollectAllStars": "<PERSON><PERSON><PERSON><PERSON><PERSON> toate stelele", "TeamMemberRemovedMessage": "Nu mai faci parte din echipa {{teamName}}, dar noi oportunități te așteaptă!", "SharedSuccess": "<PERSON> câștigat {{stars}} stele pentru că ai distribuit Biela pe {{platform}}!", "NewReferee": "Ai câștigat {{stars}} stele pentru o nouă înregistrare a unui referit!", "NewRefereePromo": "Felicitări! Ai primit {{stars}} stele ca parte a unei promoții!", "UnableToJoinTeamThisTime": "{{name}} nu poate să se alăture echipei tale de această dată!", "UserJoinedYourTeam": "{{name}} s-a alăturat echipei tale!", "YourRequestToJoinTeamDeclined": "Cererea ta de a te alătura echipei {{teamName}} a fost respinsă!", "YourRequestToJoinTeamApproved": "Cererea ta de a te alătura echipei {{teamName}} a fost aprobată!", "EngagementRejected": "Participarea ta pe {{platform}} a fost respinsă și {{amount}} stele au fost scăzute.", "ManualCreditedStars": "Un total de {{stars}} stele au fost adăugate manual în soldul tău.", "ReferralBonus": "Ai primit {{stars}} stele bonus!", "CommissionEarnedCrypto": "Cumpărarea făcută de {{name}} ți-a adus un comision de ${{amount}}, disponibil pentru retragere instantanee!", "CommissionEarnedStripe": "Cumpărarea făcută de {{name}} ți-a adus un comision de ${{amount}}! <PERSON><PERSON><PERSON> retrage începând cu {{availableToWithdrawDate}}.", "monthlyStars": "<PERSON><PERSON>", "month": {"january": "<PERSON><PERSON><PERSON>", "february": "<PERSON><PERSON><PERSON><PERSON>", "march": "<PERSON><PERSON>", "april": "<PERSON><PERSON>", "may": "<PERSON>", "june": "<PERSON><PERSON><PERSON>", "july": "<PERSON><PERSON><PERSON>", "august": "August", "september": "Septembrie", "october": "<PERSON><PERSON><PERSON>", "november": "Noiembrie", "december": "Decembrie"}, "WellBeBackSoon": "Vom reveni curând", "MaintenanceMessage": "Acest site este în curs de mentenanță. Lucrăm din greu pentru a readuce totul online cât mai curând posibil.", "ThankYouForPatience": "Vă mulțumim pentru paciența dumneavoastră.", "tooltip": {"toggle-comparison": "Comută comparația dezvoltării", "toggle-status": "Comută statusul Hackathonului"}, "publishHeck": "Publică la Hackathon", "efficiency": {"title": "Câștiguri de Eficiență", "subtitle": "Tradițional vs. Biela.dev AI", "traditional": "Tradițional", "biela": "Biela.dev AI", "cost": "Cost", "time": "<PERSON><PERSON>", "saved": "Economisit", "faster": "Mai Rapid", "profit": "Profit"}, "autoErrorHandlingTitle": "Gestionare automată a erorilor", "autoErrorHandlingDescription": "Când este activat, BIELA va gestiona și rezolva automat erorile fără a afișa modale de întrerupere. Erorile vor fi procesate în fundal și soluțiile vor fi aplicate fără probleme.", "autoErrorHandlingToggleEnabledLabel": "Activat", "autoErrorHandlingToggleDisabledLabel": "Dezactivat", "autoResolutionActiveTitle": "Rezoluție automată activă", "manualErrorHandlingTitle": "Gestionare manuală a erorilor", "autoResolutionActiveDescription": "Erorile vor fi rezolvate automat în fundal", "manualErrorHandlingDescription": "Vor apărea modale de eroare pentru revizuire și acțiune manuală", "unitTestingInterfaceTitle": "Interfață de testare unitară", "unitTestingTabVisibilityTitle": "Vizibilitatea filei de testare unitară", "unitTestingTabVisibilityDescription": "Controlează dacă fila de testare unitară apare în interfața terminalului. Când este activată, vei avea acces la comenzi de test dedicate și vizualizarea rezultatelor direct în terminal.", "unitTestingToggleVisibleLabel": "Vizibilă", "unitTestingToggleHiddenLabel": "Ascunsă", "testingTabAvailableTitle": "Fila de testare disponibilă", "testingTabHiddenTitle": "Fila de testare ascunsă", "testingTabAvailableDescription": "Comenzile și rezultatele testelor unitare sunt accesibile în terminal", "testingTabHiddenDescription": "Fila de testare unitară este ascunsă din interfața terminalului", "availableTestingFeaturesTitle": "Funcții de testare disponibile", "featureRunUnitTests": "Rulați teste unitare cu ieșire în timp real", "featureViewCoverageReports": "Vizualizați rapoarte de acoperire a testelor", "featureExecuteTestSuites": "Executați suite de teste specifice", "featureMonitorTestPerformance": "Monitorizați metrici de performanță a testelor", "errorClassificationSystemTitle": "Sistem de clasificare a erorilor", "errorClassificationSystemDescription": "BIELA utilizează un sistem codificat pe culori pentru a categoriza diferite tipuri de erori. Fiecare tip de eroare are un indicator vizual propriu pentru a te ajuta să identifici rapid sursa și natura problemei.", "databaseErrorsType": "<PERSON><PERSON><PERSON> de bază de date", "databaseErrorsDescription": "Probleme legate de operațiuni de bază de date, conexiuni și integritatea datelor.", "databaseErrorExample1": "VACUUM nu poate fi executat într-un bloc de tranzacție", "databaseErrorExample2": "Timp de conectare la baza de date expirat", "databaseErrorExample3": "Încălcare a constrângerii cheii externe", "terminalErrorsType": "Erori de terminal", "terminalErrorsDescription": "Eșecuri la executarea comenzilor, probleme de permisiuni și probleme la nivel de sistem.", "terminalErrorExample1": "npm ERR! comanda a eșuat cu codul de ieșire 1", "terminalErrorExample2": "Permisiune interzisă: nu se poate accesa fișierul", "terminalErrorExample3": "Modul nu a fost găsit sau instalarea a eșuat", "codeErrorsType": "<PERSON><PERSON><PERSON> de cod", "codeErrorsDescription": "<PERSON><PERSON><PERSON> de sintaxă, excepții la runtime și probleme de logică a programării.", "codeErrorExample1": "SyntaxError: token neașteptat", "codeErrorExample2": "TypeError: nu se poate citi proprietatea", "codeErrorExample3": "ReferenceError: variabila nu este definită", "errorHandlingBehaviorTitle": "Comportament de gestionare a erorilor", "errorHandlingBehaviorDescription": "Când gestionarea automată a erorilor este activată, aceste modale de eroare nu îți vor întrerupe fluxul de lucru. BIELA va analiza, clasifica și rezolva automat erorile în funcție de tipul și nivelul lor de severitate.", "terminationHubTitle": "Centru de terminare", "deleteProjectTitle": "Șterge proiectul", "deleteProjectWarningDescription": "Odată ce ștergi un proiect, nu mai există cale de întoarcere. Te rugăm să fii sigur.", "deleteProjectButton": "Șterge proiectul", "automaticErrorResolution": "Rezolvare automată a erorilor", "commonExamples": "Exemple comune", "aiDiffModeTitle": "<PERSON><PERSON>", "partialFileUpdatesTitle": "Actualizări parțiale de fișiere", "partialFileUpdatesDescription": "Când este activat, BIELA va actualiza numai părțile specifice ale fișierelor care necesită modificări, în loc să rescrie întregul fișier. Aceasta păstrează structura codului existent, reduce riscul de modificări nedorite și economisește credite de token la actualizarea fișierelor mari.", "statusActiveTitle": "Actualizări parțiale active", "statusInactiveTitle": "Rescriere completă a fișierelor", "statusActiveDescription": "Vor fi actualizate doar secțiunile modificate, economisind token-uri și păstrând codul", "statusInactiveDescription": "Fișierele vor fi rescrise integral atunci când sunt necesare modificări", "partialUpdateBenefitsTitle": "Beneficiile actualiz<PERSON>or <PERSON>ț<PERSON>", "benefitPreservesComments": "Păstreaz<PERSON> comentarii, formatare și structura codului", "benefitReducesRisk": "Reduce riscul de a strica funcționalitățile nemodificate", "benefitSavesTokens": "Economisește credite semnificative de token la actualizarea fișierelor mari", "benefitFasterProcessing": "Procesare mai rapidă pentru fișiere mari cu modificări mici", "benefitMaintainsHistory": "Menține granularitatea istoricului Git", "benefitPreventsRemoval": "<PERSON><PERSON><PERSON> accidentală a modificărilor personalizate", "tokenCreditEfficiencyTitle": "Eficiența creditelor de token", "tokenCreditEfficiencyDescription": "Modul Diff poate economisi până la 70–90 % din utilizarea token-urilor la actualizarea fișierelor mari prin procesarea doar a secțiunilor modificate în loc de regenerarea fișierelor întregi. Acest lucru este deosebit de benefic pentru fișiere cu peste 500 de linii de cod.", "importantNoteTitle": "Notă importantă", "importantNoteDescription": "Modul Diff funcționează cel mai bine cu cod bine structurat. Pentru refactorizări majore sau când structura fișierelor se schimbă semnificativ, BIELA poate reveni la rescrierea completă a fișierelor pentru a asigura integritatea codului.", "MyAccount": "Contul meu", "knowledgeBaseTitle": "Bază de Cunoștințe", "tabContextLabel": "Contextul Proiectului", "tabContextDescription": "Setează context personalizat și ghiduri de proiect", "tabStandardLabel": "Standardele de Cod", "tabStandardDescription": "Definește standardele de cod și bunele practici", "tabRuleLabel": "Reguli Personalizate", "tabRuleDescription": "Setează reguli și preferințe personalizate", "addNewRuleTitle": "Adaugă Regulă Nouă", "placeholderRuleTitle": "Tit<PERSON><PERSON>", "placeholderRuleDescription": "Descrier<PERSON>i", "buttonCancel": "Anulează", "buttonAddRule": "Adaugă Regulă", "activeRulesTitle": "Reguli Active", "activeRulesDescription": "BIELA.dev va folosi aceste reguli pentru a-ți îmbunătăți experiența de dezvoltare.", "emptyRulesText": "Nu a fost adăugată nicio regulă încă. Fă clic pe pictograma plus de mai sus pentru a adăuga prima ta regulă.", "modalDeleteRuleTitle": "Șterge Regula", "modalDeleteRuleMessage": "Ești sigur că vrei să ștergi \"{title}\"? Această acțiune nu poate fi anulată.", "modalDeleteActionLabel": "Șterge", "contextRule1Title": "Structura Componentelor React", "contextRule1Description": "Toate componentele trebuie să urmeze modelul de componentă funcțională cu tipuri TypeScript adecvate.", "standardRule2Title": "Formatarea Codului", "standardRule2Description": "Folosește 2 spații pentru indentare și virgule finale în obiecte pe mai multe linii.", "customRule3Title": "Gestionarea Stării", "customRule3Description": "Folosește React Context pentru starea globală și starea locală pentru date specifice componentei.", "developerTools": "Instrumente pentru dezvoltatori"}