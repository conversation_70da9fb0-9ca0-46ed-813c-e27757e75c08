import { useEffect, useRef, useState } from 'react';
import { useNavigate, useSearchParams, useParams } from 'react-router-dom';
import { UserStore } from '~/ai/lib/stores/user/userStore';
import { useUser } from '~/ai/lib/context/userContext';
import { LoadingBiela } from '~/components/LoadingBiela';

export const allowedProviders = ['google', 'github', 'paypal'] as const;
export type Provider = (typeof allowedProviders)[number];

export default function Auth() {
  const navigate = useNavigate();
  let params = useParams();
  const [searchParams] = useSearchParams();
  const userStore = UserStore.getInstance();
  const [isSuccess, setIsSuccess] = useState(false);

  const { isLoggedIn } = useUser();
  const redirectUrl = searchParams.get('redirectUrl');
  const provider = params['auth-provider'] as Provider;
  const hasRun = useRef(false);

  const redirectAfterLogin = () => {
    if (redirectUrl) {
      const newUrl = new URL(redirectUrl);
      window.location.href = newUrl.toString();
    } else {
      const chatId = localStorage.getItem('chatId');
      navigate(chatId ? `/chat/${chatId}` : '/');
    }
  };

  const handleSucces = () => {
    setIsSuccess(true);
    setTimeout(redirectAfterLogin, 4300);
  };

  const auth = (code: string) => {
    switch (provider) {
      case 'google':
        const state = searchParams.get('state');
        return userStore.googleLogin(code, state!).then(handleSucces);
      case 'github':
        return userStore.githubLogin(code).then(handleSucces);
      case 'paypal':
        return userStore.paypalLogin(code).then(handleSucces);
      default:
        break;
    }
  };

  useEffect(() => {
    const code = searchParams.get('code') as string;

    if (isLoggedIn()) {
      return navigate('/');
    }

    if (!code || !provider || !allowedProviders.includes(provider)) {
      navigate('/?login=true');
      return;
    }

    if (hasRun.current) {
      return;
    }

    hasRun.current = true;

    auth(code);
  }, []);

  return (
    <div
      className="min-h-screen flex items-center flex-col justify-center p-4 bg-auth"
      style={{
        backgroundImage: 'url(/hero-bg-shade-empty.png)',
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
      }}
    >
      {isSuccess ? <LoadingBiela /> : <></>}
    </div>
  );
}
