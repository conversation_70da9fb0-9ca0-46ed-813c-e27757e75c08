import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { fetchProjects } from '~/api/projectsApi';
import AffiliateBanner from '~/backOffice/components/dashboard/AffiliateBanner';
import ProjectsTypeFilter from '~/backOffice/components/dashboard/ProjectsTypeFilter';
import Wrapper from '~/components/common/Wrapper';
import ClientOnly from '~/components/common/ClientOnly';
import ProjectsListClient from '~/backOffice/components/dashboard/ProjectsList.client';
import QuickActions from '~/backOffice/components/dashboard/QuickActions.client';
import { useUser } from '~/ai/lib/context/userContext';
import { useNavigate } from 'react-router-dom';
import '~/components/styles/contest-btn.css';
import JoinUsLiveYoutube from '~/components/banners/JoinUsLiveYoutube';
import UpgradePlanPromotion from '~/components/banners/UpgradePlanPromotion';
import { fetchCurrentUserPlan } from '~/lib/stores/billing';
import { Plan } from '~/types/billing';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';

type Project = {
  id: number;
  title: string;
  createdAt: string;
  type: string;
};

function Dashboard() {
  const { t } = useTranslation('translation');
  const { isLoggedIn } = useUser();
  const navigate = useNavigate();
  const [dashboardData, setDashboardData] = useState<{ quickActions: any[]; folders: any[]; projects: Project[] }>({
    quickActions: [],
    folders: [],
    projects: [],
  });
  const [selectedType, setSelectedType] = useState<string>('all');
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [filterLoading, setFilterLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [showNewFolderInput, setShowNewFolderInput] = useState<boolean>(false);
  const [selectedFolder, setSelectedFolder] = useState<string>('');
  const [publishedProjects, setPublishedProjects] = useState<string[]>([]);
  const [refreshContestProjects, setRefreshContestProjects] = useState(0);
  const [searchTermChanged, setSearchTermChanged] = useState(false);
  const [plan, setPlan] = useState<Plan | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [totalPagesNumber, setTotalPagesNumber] = useState(0);
  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage - 1);
    setLoading(true);
    fetchProjects(selectedFolder || undefined, newPage - 1, 10, 'updatedAt', 'DESC')
      .then((data) => {
        setTotalPagesNumber(data.totalPages);
        const items = data.items.map((p) => ({
          ...p,
          projectName: p.projectName || p.projectSlug,
        }));
        setDashboardData((prev) => ({ ...prev, projects: items }));
        setFilteredProjects(items);
      })
      .catch(console.error)
      .finally(() => setLoading(false));
  };

  function getPagination(current: number, total: number) {
    const delta = 2;
    const range: (number | '...')[] = [];
    const left = Math.max(2, current - delta);
    const right = Math.min(total - 1, current + delta);
    range.push(1);
    if (left > 2) range.push('...');
    for (let i = left; i <= right; i++) range.push(i);
    if (right < total - 1) range.push('...');
    if (total > 1) range.push(total);
    return range;
  }

  const triggerRefreshContestProjects = () => {
    setRefreshContestProjects((prev) => prev + 1);
  };

  const updatePublishedProjects = (projectSlug: string, isPublished: boolean) => {
    setPublishedProjects((prev) =>
      isPublished ? [...prev, projectSlug] : prev.filter((slug) => slug !== projectSlug)
    );
  };

  useEffect(() => {
    const loadPlan = async () => {
      try {
        const userPlan = await fetchCurrentUserPlan();
        setPlan(userPlan);
      } catch {}
    };

    loadPlan();
  }, []);

  useEffect(() => {
    if (loading && searchTermChanged) {
      return;
    }

    const loadData = async () => {
      try {
        setLoading(true);

        let data;
        if (selectedFolder) {
          data = await fetchProjects(selectedFolder, currentPage, 10, 'updatedAt', 'DESC');
        } else {
          data = await fetchProjects(undefined, currentPage, 10, 'updatedAt', 'DESC');
        }
        setTotalPages(data.totalPages);

        const projects = data.items.map((project) => ({
          ...project,
          projectName: project.projectName || project.projectSlug,
        }));
        setDashboardData((prevData) => ({ ...prevData, projects }));
        setFilteredProjects(projects);
        setSearchTermChanged(false);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        setError(error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [selectedFolder, currentPage]);

  const refreshProjects = async () => {
    try {
      setLoading(true);
      let data = await fetchProjects(selectedFolder, currentPage, 10, 'updatedAt', 'DESC');
      setTotalPages(data.totalPages);
      const projects = data.items.map((project) => ({
        ...project,
        projectName: project.projectName || project.projectSlug,
      }));
      setDashboardData((prevData) => ({ ...prevData, projects }));
      setFilteredProjects(projects);
    } catch (error) {
      console.error('Error refreshing projects:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = async (type: string) => {
    setFilterLoading(true);
    setSelectedType(type);
    setSelectedFolder(type === 'all' ? '' : type);
    setCurrentPage(0);

    try {
      const data = await fetchProjects(type === 'all' ? null : type, 0, 10, 'updatedAt', 'DESC');

      setTotalPages(data.totalPages);
      const projects = data.items.map((project) => ({
        ...project,
        projectName: project.projectName || project.projectSlug,
      }));

      setFilteredProjects(projects);
      setDashboardData((prev) => ({ ...prev, projects }));
    } catch (error) {
      console.error('Error filtering projects:', error);
    } finally {
      setFilterLoading(false);
    }
  };

  useEffect(() => {
    document.title = t('meta.dashboard.title') || 'Your Projects Dashboard – biela.dev';
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        'content',
        t('meta.dashboard.description') ||
          'Manage your AI-built websites and apps, edit live projects, and track your build history—all in one place.'
      );
    }
  }, [t]);

  return (
    <Wrapper
      handleSendMessage={(e, messageInput) => console.log('Message sent:', messageInput)}
      isStreaming={false}
      isDashboardPage={true}
      handleStop={() => console.log('Stream stopped')}
    >
      <div className="w-full min-h-screen bg-[transparent]">
        <div className="min-h-screen background-image-full color-white flex flex-col">
          <div className="container mx-auto px-8 py-8 max-w-[1600px] space-y-8">
            <div className="md:flex items-center justify-between">
              <h1 className="text-4xl font-light uppercase">{t('dashboard', 'Dashboard')}</h1>
            </div>
            {plan?.name ? <JoinUsLiveYoutube /> : <UpgradePlanPromotion />}
            <ClientOnly>
              {() => (
                <QuickActions actions={dashboardData.quickActions} setShowNewFolderInput={setShowNewFolderInput} />
              )}
            </ClientOnly>
            <AffiliateBanner />
            <div className="flex flex-col xl:flex-row gap-6 lg:flex-col">
              <ProjectsTypeFilter
                projects={dashboardData.projects}
                onFilterChange={handleFilterChange}
                showNewFolderInput={showNewFolderInput}
                setShowNewFolderInput={setShowNewFolderInput}
                selectedFolder={selectedFolder}
                refreshProjects={refreshProjects}
              />
              <ClientOnly key={selectedType}>
                {() => (
                  <ProjectsListClient
                    nextPage={(page) => {
                      setCurrentPage(page);
                    }}
                    totalPagesNumber={totalPages}
                    setTotalPagesNumber={setTotalPages}
                    projects={filteredProjects}
                    selectedType={selectedType}
                    selectedFolder={selectedFolder}
                    loading={loading}
                    refreshProjects={refreshProjects}
                    filterLoading={filterLoading}
                    publishedProjects={publishedProjects}
                    updatePublishedProjects={updatePublishedProjects}
                    triggerRefreshContestProjects={triggerRefreshContestProjects}
                    currentPage={currentPage + 1}
                    onPageChange={handlePageChange}
                    getPagination={getPagination}
                  />
                )}
              </ClientOnly>
            </div>
            {totalPages > 1 && (
              <div style={{ zIndex: '20', position: 'relative' }} className="flex justify-center items-center mt-6">
                <button
                  onClick={() => handlePageChange(currentPage === 0 ? totalPages : currentPage)}
                  className="p-2 bg-[#123456] hover:bg-gradient-to-r from-green-500/20 to-green-600/20 rounded-lg"
                >
                  <FaChevronLeft />
                </button>

                <div className="flex gap-2 mx-4">
                  {getPagination(currentPage + 1, totalPages).map((n, idx) =>
                    n === '...' ? (
                      <span key={`ellipsis-${idx}`} className="px-2 text-gray-400 select-none">
                        ...
                      </span>
                    ) : (
                      <button
                        key={n}
                        onClick={() => handlePageChange(Number(n))}
                        className={`p-2 px-3.5 rounded-lg ${
                          currentPage + 1 === Number(n)
                            ? 'bg-gradient-to-r from-green-500 to-green-600 text-white'
                            : 'bg-[#123456] hover:bg-gradient-to-r from-green-500/20 to-green-600/20'
                        }`}
                      >
                        {n}
                      </button>
                    )
                  )}
                </div>

                <button
                  onClick={() => handlePageChange(currentPage + 2 > totalPages ? 1 : currentPage + 2)}
                  className="p-2 bg-[#123456] hover:bg-gradient-to-r from-green-500/20 to-green-600/20 rounded-lg"
                >
                  <FaChevronRight />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </Wrapper>
  );
}

export default Dashboard;
