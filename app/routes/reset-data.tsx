import { useEffect, useState } from 'react';
import { LoadingBiela } from '~/components/LoadingBiela';
import { useNavigate } from 'react-router-dom';
import { useUser } from '~/ai/lib/context/userContext';

type ResetStatus = 'idle' | 'processing' | 'success' | 'error';

const ResetData = () => {
  const [status, setStatus] = useState<ResetStatus>('idle');
  const navigate = useNavigate();
  const { logout } = useUser();
  const doLogout = async () => {
    await logout();
    navigate('/');
  };
  const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

  const clearCookies = () => {
    document.cookie.split(';').forEach((cookie) => {
      const name = cookie.split('=')[0].trim();
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
    });
  };

  const clearIndexedDB = async () => {
    if ('databases' in indexedDB) {
      const databases = await indexedDB.databases();
      for (const db of databases) {
        if (db.name) {
          indexedDB.deleteDatabase(db.name);
        }
      }
    }
  };

  const resetAndRedirect = async () => {
    try {
      setStatus('processing');

      await new Promise(requestAnimationFrame);

      localStorage.clear();
      clearCookies();
      await clearIndexedDB();

      setStatus('success');
      await delay(4300);
      await doLogout();
    } catch (error) {
      console.error('Error resetting ', error);
      setStatus('error');
      await delay(4300);
      window.location.reload();
    }
  };

  useEffect(() => {
    resetAndRedirect();
  }, []);

  return (
    <div
      className="min-h-screen flex items-center flex-col justify-center p-4 bg-auth"
      style={{
        backgroundImage: 'url(/hero-bg-shade-empty.png)',
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
      }}
    >
      {status === 'success' && (
        <>
          <LoadingBiela />
        </>
      )}
      {status === 'error' && (
        <p className="text-red-500 text-lg animate-pulse text-center max-w-md">
          A aparut o eroare la stergerea datelor.
          <br />
          Se reincarca pagina automat
        </p>
      )}
    </div>
  );
};

export default ResetData;
