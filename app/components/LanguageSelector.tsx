import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AnimatePresence, motion } from 'framer-motion';
import { FaCheck, FaChevronDown, FaGlobe } from 'react-icons/fa';

interface Language {
  code: string;
  name: string;
  flag: string;
}

interface LanguageSelectorProps {
  large?: boolean;
  withBorder?: boolean;
  hideText?: boolean;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ hideText = false, large = false, withBorder = false }) => {
  const { i18n } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const languages: Language[] = [
    { code: 'en', name: 'English', flag: '/flags/en.png' },
    { code: 'es', name: '<PERSON>spa<PERSON><PERSON>', flag: '/flags/es.png' },
    { code: 'fr', name: 'Français', flag: '/flags/fr.png' },
    { code: 'de', name: '<PERSON><PERSON><PERSON>', flag: '/flags/de.png' },
    { code: 'it', name: 'Italiano', flag: '/flags/it.png' },
    { code: 'pt', name: 'Português', flag: '/flags/pt.png' },
    { code: 'ru', name: 'Русский', flag: '/flags/ru.png' },
    { code: 'ro', name: 'Română', flag: '/flags/ro.png' },
    { code: 'zh', name: '中文', flag: '/flags/zh.png' },
    { code: 'ja', name: '日本語', flag: '/flags/ja.png' },
    { code: 'ko', name: '한국어', flag: '/flags/ko.png' },
    { code: 'ar', name: 'العربية', flag: '/flags/ar.png' },
    { code: 'az', name: 'Azərbaycan', flag: '/flags/az.png' },
    { code: 'in', name: ' हिन्दी', flag: '/flags/in.png' },
  ];

  const currentLanguage = languages.find((lang) => lang.code === i18n.language) || languages[0];

  const setUserLanguageCookie = (langCode: string) => {
    document.cookie = `user-language-cookie=${langCode}; path=/; max-age=31536000`;
  };

  const getUserLanguageCookie = (): string | null => {
    const match = document.cookie.match(/(^|;) ?user-language-cookie=([^;]*)(;|$)/);
    return match ? match[2] : null;
  };

  const changeLanguage = (langCode: string) => {
    i18n.changeLanguage(langCode);
    setUserLanguageCookie(langCode);
    setIsOpen(false);
  };

  useEffect(() => {
    const savedLang = getUserLanguageCookie();
    if (savedLang && savedLang !== i18n.language) {
      i18n.changeLanguage(savedLang);
    }
  }, [i18n]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative focus-invisible" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`text-sm px-4 py-2 rounded-md sm:w-[150px] relative text-white language-selector-button flex gap-2 items-center hover:bg-white/20 transition-colors cursor-pointer`}
        style={{ backgroundColor: '#19263d' }}
      >
        <FaGlobe className="text-sm" />
        <span className={`max-sm:hidden flex-1 flex flex-row gap-2  items-center font-light text-sm false`}>
          {currentLanguage.name}
        </span>
        <FaChevronDown className={`text-xs transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.95 }}
            transition={{ duration: 0.1 }}
            className={`absolute right-0 mt-2 w-[150px] bg-[#101828] rounded-lg border border-gray-800 overflow-x-hidden vertical-scroll overflow-y-auto z-50 false`}
          >
            <div className="py-1 max-h-[300px]">
              {languages.map((language) => (
                <button
                  key={language.code}
                  onClick={() => changeLanguage(language.code)}
                  className="w-full text-left pl-[16px] cursor-pointer pr-[15px] py-2 text-white/80 hover:bg-gray-800/50 transition-colors flex items-center justify-between"
                >
                  <span className="font-light flex flex-row items-center gap-2">{language.name}</span>
                  {language.code === i18n.language && <FaCheck className="text-green-400 text-sm" />}
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default LanguageSelector;
