@use 'variables';
@use 'z-index';
@use 'animations';
@use 'terminal';
@use 'resize-handle';
@use 'code';
@use 'editor';
@use 'toast';

@font-face {
  font-family: 'Rexton Regular';
  src: url('/fonts/Rexton/Rexton Regular.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: 'Rexton Light';
  src: url('/fonts/Rexton/Rexton Light.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}

@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');

@font-face {
  font-family: 'Manrope';
  src: url('/fonts/Manrope/Manrope-Regular.ttf') format('truetype');
  font-weight: normal;
  font-size: normal;
}
@layer utilities {
  .scrollbar-hide {
    /* Internet Explorer 10+ */
    -ms-overflow-style: none;
    /* Firefox */
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    /* Chrome, Safari, Opera */
    display: none;
  }
}

html,
body {
  height: 100%;
  width: 100%;
  font-family: 'Manrope','sans-serif';
  background-color: #0A0F1C !important;
}

// .cm-scroller, .xterm-viewport {
//   scrollbar-width: none;
//   -ms-overflow-style: none;
// }
// .cm-scroller::-webkit-scrollbar, .xterm-viewport::-webkit-scrollbar {
//   width: 0;
//   height: 0;
// }
.embossed-logo {
  width: 100%;
  position: relative;
}
.hover-lines:hover {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 75%, transparent 75%, transparent);
  background-size: 8px 8px;
}
.animate-green-btn {
  min-width: 300px;
}
.embossed-logo svg {
  height: auto;
  /* Enhanced letterpress effect with multiple layers */
  filter:
    /* Primary inset shadow to create depth */
    drop-shadow(0px -1.5px 0px rgba(0, 0, 0, 0.9))
      /* Secondary inset shadow for more depth */
    drop-shadow(0px -0.5px 0px rgba(0, 0, 0, 0.7))
      /* Subtle highlight at bottom to create dimension */
    drop-shadow(0px 1px 0px rgba(255, 255, 255, 0.05));
}
.close-modal:focus {
  outline: unset;
}
.close-modal:hover {
  transform: scale(1.2);
}
/* Styling for the SVG paths to enhance letterpress effect */
.embossed-logo .cls-1 {
  /* Further brightened fill color while maintaining the embossed effect */
  fill: #1a3a6c;
  opacity: 1;
}
.background-biela {
  position: fixed;
  inset: 0;
  background-color: #0A1730;
  svg {
    max-width: 100vw;
    max-height: 100vh;
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    inset: 0;
    z-index: 2;
    margin: auto;
  }
}
.background-biela:before {
  position: absolute;
  content: '';
  width: clamp(300px, 41.67vw, 600px);
  height: clamp(300px, 41.67vw, 600px);
  right: clamp(-250px, -17.36vw, -100px);
  top: clamp(-250px, -20.83vw, -100px);
  border-radius: 100%;
  background: #16A249;
  filter: blur(174.16734313964844px);
}
.ellipsis-text-name {
  max-width: 400px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
/*LOGO LOADING LOGIN*/
.logo-container-login {
  width: 300px;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 60px;
}
.loading-container {
  width: 100%;
  max-width: 1200px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.cls-1 {
  fill: #0a1730;
}

.cls-2 {
  fill: #4ade80;
}

.logoLogin {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transform: rotate(-72deg);
  position: relative;
  z-index: 2;
}

.trail-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;
}

.trail {
  position: absolute;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(74, 222, 128, 0.8) 0%, rgba(74, 222, 128, 0) 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
}

.b-letter {
  transform-box: fill-box;
  transform-origin: center;
}

/* Animation keyframes */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

@keyframes throttle {
  0% {
    stroke-dashoffset: 1000;
    opacity: 0.3;
  }
  100% {
    stroke-dashoffset: 0;
    opacity: 1;
  }
}

@keyframes starTwinkle {
  0% {
    opacity: 0.2;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.2;
    transform: scale(0.8);
  }
}

@keyframes trailEffect {
  0% {
    width: 0;
    height: 0;
    opacity: 0;
  }
  50% {
    width: 150px;
    height: 150px;
    opacity: 0.7;
  }
  100% {
    width: 300px;
    height: 300px;
    opacity: 0;
  }
}
@keyframes bGlow {
  0%, 100% {
    opacity: 0.8;
    filter: drop-shadow(0 0 4px rgba(74, 222, 128, 0.4));
  }
  50% {
    opacity: 1;
    filter: drop-shadow(0 0 12px rgba(74, 222, 128, 0.8));
  }
}

.b-letter {
  transform-box: fill-box;
  transform-origin: center;
}

@keyframes pathGlow {
  0% {
    filter: drop-shadow(0 0 2px rgba(74, 222, 128, 0.5));
  }
  50% {
    filter: drop-shadow(0 0 15px rgba(74, 222, 128, 0.8));
  }
  100% {
    filter: drop-shadow(0 0 2px rgba(74, 222, 128, 0.5));
  }
}

@keyframes flyAway {
  0% {
    transform: rotate(-72deg) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: rotate(-72deg) scale(0.1) translate(1000px, -1000px);
    opacity: 0;
  }
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.aligned-menu-header {
  display: flex;
  align-items: center;
  gap: 20px;
}
.blur-effect-card:nth-of-type(3), .blur-effect-card:nth-of-type(4) {
  --un-blur: blur(4px);
  filter: var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);
}
.rexton-light {
  font-family: 'Rexton Light', sans-serif !important;
}
.font-manrope {
  font-family: 'Manrope', sans-serif !important;
}
.search-input svg {
  position: absolute;
  right: 10px;
  top: 12px;
  color: #fff;
}
:root {
  --gradient-opacity: 0.8;
}
.absolute-length {
  position: absolute;
  right: -4%;
  top: -8%;
  z-index: 999;
}
:root[data-theme='dark'] {
  background-color: black;
}
.send-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.send-button.isProcessing {
  overflow: visible;
  background: transparent;
  padding: 0;
  border: 1px solid #2b2f38;
  border-radius: 0;
}
.bg-accent\/20 {
  background-color: #22c55e33;
  opacity: 60%;
}
.duration-300 {
  transition-duration: .3s;
}
.cropper-face.cropper-move,
.circle-cropper .cropper-crop-box,
.circle-cropper .cropper-view-box {
  border-radius: 50% !important;
}
.circle-cropper .cropper-view-box {
  border-radius: 50% !important;
  border: 2px solid rgb(34, 197, 94) !important; /* Verde custom */
  box-shadow: none !important;
}

/* Elimină marginile albastre din crop-box */
.circle-cropper .cropper-crop-box {
  border-radius: 50% !important;
}
.send-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  background: radial-gradient(circle, rgba(74, 222, 128, 0.2) 0%, transparent 70%);
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.5s ease;
}
.blur-bg {
  background-color: #0b0e14cc;
  --tw-backdrop-blur: blur(16px);
  transition: backdrop-filter 0.3s ease, background-color 0.3s ease;
}
.new-bg-chat {
  background-color: #0a0f1c;
}
img.hover-zoom:hover {
  transform: scale(1.05);
  transition-duration: 0.3s;
}
.send-button:hover::before {
  transform: translate(-50%, -50%) scale(1);
}

.send-button.processing {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.send-button.processing::before {
  background: radial-gradient(circle, rgba(239, 68, 68, 0.2) 0%, transparent 70%);
}
.search-folder:focus {
  outline: unset;
}
.search-folder {
  background-color: #202739;
  border: unset;
}
.search-folder::placeholder {
  color: #fff;
  font-weight: 300;
}
.text-logo {
  display: flex;
  justify-content: flex-end;
  flex-direction: column;
}
.space-padding-file {
  padding: 2px;
}

svg.device-btn-icon {
  max-width: 22px;
  filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(7499%) hue-rotate(316deg) brightness(103%) contrast(104%);
}

.text-logo-sm {
  font-size: 10px;
  height: 1px;
}

.i-ph\:microphone-slash {
  background-color: #fff !important;
}

.micro-on {
  width: 23px !important;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px 14px !important;

  &:hover {
    padding: 2px 12px;
    width: 25px !important;
  }
}

.hover-magic-effect:hover {
  background-color: #37373a;
  border-radius: 0.375rem;
  cursor: pointer;
  padding: 0.25rem;
}

.hover-magic-effect {
  padding: 0.25rem;
  background-color: transparent;

  img {
    width: 16px;
  }
}
.manrope{
  font-family: 'Manrope', sans-serif !important;
}

//.too-much-text:before {
//  content: "";
//  position: absolute;
//  inset: 0;
//  border-radius: 12px;
//  padding: 2px;
//  background: linear-gradient(150deg, rgba(255, 0, 0, 1) 0%, rgba(255, 0, 0, 0.5) 10%, rgba(34, 32, 38, 1) 20%, rgba(34, 32, 38, 1) 80%, rgba(255, 0, 0, 0.5) 90%, rgba(255, 0, 0, 1) 100%);
//  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
//  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
//  -webkit-mask-composite: xor;
//  pointer-events: none; /* Allow clicks to pass through */
//}

.too-much-text {
  position: absolute;
}
//.too-much-text:before {
//  content: "";
//  position: absolute;
//  inset: 0;
//  border-radius: 12px;
//  padding: 2px;
//  background: linear-gradient(150deg, rgba(255,0,0,1) 0%, rgba(255,0,0,0.5) 10%, rgba(34,32,38,1) 20%, rgba(34,32,38,1) 80%, rgba(255,0,0,0.5) 90%, rgba(255,0,0,1) 100%);
//  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
//  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
//  -webkit-mask-composite: xor;
//}
// glass effect for buttons
.glass-button {
  position: relative;
  backdrop-filter: blur(5px);
  overflow: hidden;
  transition: all 0.3s ease;
}

.glass-button::before {
  content: '';
  position: absolute;
  width: 80px;
  height: 150%;
  //background: linear-gradient(65deg, transparent, rgba(255, 255, 255, 0.545), transparent);
  filter: blur(8px);
  top: -25%;
  right: -100px;
  transform: skewX(-30deg);
  transition: transform 1s ease-in-out;
}

.glass-button:hover::before {
  transform: translateX(-500px) skewX(-50deg);
}

.max-width-edit {
  width: 100%;
}
.scroll-container {
  overflow: hidden;
  max-width: 270px;
  position: relative;
}

.scroll-text {
  white-space: nowrap;
  display: inline-block;
  transition: transform 6s linear;
}

.scroll-container:hover .animate-scroll {
  transform: translateX(calc(-100% + 270px));
}


.current-description {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 250px;
}
button.flex.items-center.rounded-lg.transition-all.duration-300.relative.bg-gradient-to-r.from-biela-green.to-biela-hover.text-white.py-1\.5.px-3.font-normal.shadow-md.hover\:shadow-lg.hover\:from-biela-hover.hover\:to-biela-green.hover\:scale-105.group {
  background-color: #0bb57c;
  background-image: unset;
}
#project-tooltip,
.react-tooltip {
  background-color: #222d40 !important;
  color: #fff;
  padding: 5px 10px;
  border-radius: 4px;
  position: absolute;
  white-space: nowrap;
  z-index: 1000;
  pointer-events: none;
}
.action-group {
  display: flex;
  align-items: center;
  padding: 0.125rem 0.625rem;
  background-color: #232e41;
  border: 1px solid #293447;
  border-radius: 0.5rem;
}
.action-group svg {
  color: #94a2b5;
}
.action-group .group:hover svg {
  color: rgb(248, 250, 252);
}
.action-group .group:hover {
  transform: scale(1.05);
  color: rgb(248, 250, 252);
}
button.w-full.hover\:bg-gradient-to-r.from-gray-800\/50.to-gray-900\/50.backdrop-blur-sm.rounded-xl.p-3.lg\:p-4.text-left.transition-all.bg-gray-800\/70.border.border-gray-700\/50.flex.items-center.justify-between {
  background-color: #293445;
}
select::-ms-expand {
  display: none;
}
button.w-full.hover\:bg-gradient-to-r.from-gray-800\/50.to-gray-900\/50.backdrop-blur-sm.rounded-xl.p-3.lg\:p-4.text-left.transition-all.bg-gray-800\/70.border.border-gray-700\/50.flex.items-center.justify-between:hover {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 75%, transparent 75%, transparent);
  background-size: 8px 8px;
}
.cancel-btn-modal {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.05) 75%, transparent 75%, transparent);
  background-size: 8px 8px;
}
button.p-2.hover\:bg-gray-800.rounded-full.transition-colors.bg-transparent svg {
  color: #a3a3a3;
}
button.flex.items-center.rounded-lg.transition-all.duration-300.relative.bg-\[\#0B1931\].text-white.py-1\.5.px-2\.5.shadow-md.hover\:bg-\[\#152A4A\].hover\:shadow-lg.hover\:scale-105.group svg {
  color: #fff;
}
.arrow-absolute svg {
  position: absolute;
  right: 50px;
}
.arrow-absolute svg {
  position: relative;
}
.action-group > * + * {
  margin-left: 0.25rem;
}
button.flex.items-center.rounded-lg.transition-all.duration-300.relative.bg-app-hover\/20.text-app-icon-default.p-1\.5.border.border-app-hover\/30.backdrop-blur-sm.hover\:bg-app-hover\/40.hover\:text-app-icon-hover.hover\:border-app-hover.hover\:scale-105.group {
  background-color: #273548;
  border: 1px solid #222c41;
}
button.flex.items-center.rounded-lg.transition-all.duration-300.relative.bg-app-icon-danger\/10.text-app-icon-danger.p-1\.5.border.border-app-icon-danger\/30.hover\:bg-app-icon-danger\/20.hover\:border-app-icon-danger.hover\:scale-105.pulse-on-hover.group {
  background-color: #363242;
  border: 1px solid #e04959;
}
button.flex.items-center.rounded-lg.transition-all.duration-300.relative.bg-app-icon-danger\/10.text-app-icon-danger.p-1\.5.border.border-app-icon-danger\/30.hover\:bg-app-icon-danger\/20.hover\:border-app-icon-danger.hover\:scale-105.pulse-on-hover.group svg {
  color: #e04959;
}
.three-buttons{
  background: rgba(255, 255, 255, .03) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  backdrop-filter: blur(10px) !important;
  font-size: 12px;
}
.three-buttons:hover{
  background: white !important;
  padding: 12px;
}
.custom {
  font-family: 'Rexton Light', sans-serif;
  letter-spacing: -0.2em;
}
.project-action-tooltip {
  background-color: #0A0F1C !important;
  color: #E5E7EB;
  font-size: 0.75rem;
  line-height: 1rem;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.overflow-hidden.border-white\/5.bg-\[\#121724\].p-4.lg\:p-6.border {
  margin: 0 !important;
}
.absolute-buttons-check {
  position: absolute;
  top: 30%;
  right: 80px;
}
.delete-chat-desc {
  color: #F5F5F5;
  font-family: 'Manrope', sans-serif;
  font-size: 16px;
  font-style: normal;
  font-weight: 300;
  line-height: 40px;
  padding-top: 0;
}
.affiliate-name-class {
  max-width: 300px;
}
.animated-logo {
  position: relative;
  min-width: 40px;
  min-height: 40px;

  img {
    position: absolute;
    object-fit: contain;
  }

  .spiral {
    width: 40px;
    height: 40px;
    left: 0;
    top: 0;
    transform-origin: center;
    animation: spiral-anim 5s linear infinite;
  }

  .inner {
    width: calc(100% - 10px);
    height: calc(100% - 10px);
    left: 5px;
    top: 5px;
    transform-origin: center;
    animation: inner-anim 5s linear infinite;
  }

  @keyframes inner-anim {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(-360deg);
    }
  }

  @keyframes spiral-anim {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

}
.text-base-input {
  max-width: 150px;
}
@media(max-width: 1500px) {
  .ellipsis-text-name {
    max-width: 180px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
}
@media(max-width: 1600px) {
  .full-banner button {
    max-width: 300px;
  }
  .full-banner {
    flex-direction: column;
  }
  .lifetime-cols {
    width: -webkit-fill-available;
    justify-content: space-between;
  }
  .join-affiliate {
    width: 100%;
  }
  .full-banner a {
    justify-content: left;
  }
}
@media(min-width: 1300px) {
  input.text-base.font-light.bg-transparent.focus\:outline-none.px-1.py-1.min-w-0.flex-1.text-white {
    max-width: 177px;
  }
}
@media(max-width: 1300px) {
  .text-base-input {
    max-width: unset;
  }
  .responsive-affiliate {
    display: flex;
    flex-direction: column;
  }
  .gap-ultimate div.flex.items-center.gap-3,
  .gap-ultimate {
    width: 100%;
    justify-content: center;
  }
  .affiliate-name-class {
    max-width: unset;
  }
  .full-banner a {
    justify-content: center;
    display: flex;
  }
  .flex-icons-dash {
    display: flex;
    flex-direction: column;
    gap: 18px;
    align-items: flex-start !important;
  }
}
@media screen and (max-width: 500px) {
  .project-name-container {
    justify-content: center;

    .project-name-container-span {
      max-width: 200px;
    }
  }
}
@media(max-width: 1200px) {
  .responsive-actions {
    display: flex !important;
    flex-direction: column;
  }
}
@media(max-width: 991px) {
  .space-y-4 .space-y-2 {
    margin-bottom: 10px !important;
  }
}
@media(max-width: 600px) {
  .title-responsive-mob {
    max-width: 200px;
  }
}

.animated-logo.bouncing {
  .inner {
    animation: bounce-inner-anim 5s linear infinite !important;
  }

  .spiral {
    animation: bounce-spiral-anim 5s linear infinite !important;
  }

  @keyframes bounce-inner-anim {
    0% {
      transform: scale(0) rotate(0deg);
    }
    4% {
      transform: scale(1.1) rotate(0deg);
    }
    5% {
      transform: scale(1) rotate(0deg);
    }
    100% {
      transform: scale(1) rotate(-360deg);
    }
  }

  @keyframes bounce-spiral-anim {
    0% {
      transform: scale(0) rotate(0deg);
    }
    5% {
      transform: scale(0) rotate(0deg);
    }
    10% {
      transform: scale(1.1) rotate(0deg);
    }
    11% {
      transform: scale(1) rotate(0deg);
    }
    /* Faza de spin (2–5 s) */
    100% {
      transform: scale(1) rotate(360deg);
    }
  }


}

@media(max-width: 750px) {
  .modal-delete-chat {
    max-width: 450px !important;
  }
}

.modal-delete-chat {
  width: 100%;
  max-width: 650px;
  border-radius: 8px;
  border: 1px solid #2E2E2E;
  backdrop-filter: blur(8px);
}

.footer-modal {
  justify-content: center;
  gap: 16px;
}

.cancel-footer {
  border-radius: 500px;
  border: 1px solid #777;
  background: #030303;
  font-family: 'Manrope', sans-serif;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  padding: 16px 24px;
  width: 100%;
  max-width: 190px;
}
.bg-black\/50.fixed.inset-0.z-40 {
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.05) 0%, rgba(31, 31, 31, 0.80) 100%);
  backdrop-filter: blur(25px);
}

.cancel-footer:nth-of-type(2) {
  border-radius: 500px;
  background-color: transparent;
  border: 1px solid #FF554A;
  color: #FFF;
  font-family: 'Manrope', sans-serif;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;

  &:hover {
    background-color: #FF554A;
    border: 1px solid #FF554A;
  }
}



.delete-chat-title {
  color: #F5F5F5;
  font-family: "Rexton Light", sans-serif;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 32px;
  border-bottom: 0;
  padding-bottom: 12px;
}
.copyright-teachme {
  color: #FFF;
  text-align: center;
  font-family: 'Manrope', 'sans-serif';
  align-items: center;
  font-size: 12px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
  padding-top: 16px;
  margin-bottom: 24px;
}

.shadow-xl {
  .left-menu-open {
    display: flex;
    gap: 8px;
    padding: 21px 1.25rem;
  }
}

.height-write {
  height: 84px !important;
}

.biela-send {
  img {
    filter: brightness(0) saturate(100%) invert(0%) sepia(95%) saturate(0%) hue-rotate(109deg) brightness(99%) contrast(105%);
  }
}

.sky-absolute {
  position: absolute;
  z-index: 1;
  max-width: 900px;
  top: 30%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.chat-textarea-font::placeholder {
  font-family: 'Manrope', 'sans-serif';
  font-size: 14px;
  font-style: normal;
  font-weight: 300;
  line-height: normal;
}

.entire-chat {
  display: flex;
  flex-direction: column;
  justify-content: space-between !important;
  //padding-top: 48px;
  z-index: unset !important;
}


.text-biela-elements-textSecondary {
  color: #D5D5D5;
  text-align: center;
  font-family: 'Manrope', sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 300;
  line-height: 24px;
  letter-spacing: 0;
}

.chat-style-apply .chat-on-error.base-chat {
  .green-light {
    background-color: #F10201;
  }
  .error-details {
    transition: all 0.3s ease-in-out;

  }
  .error-details.hide {
    padding: 0;
    margin: 0;
    max-height: 0;
    overflow: hidden;
  }

}

.chat-mode {
  margin-bottom: 0;
  max-width: 670px;
  display: flex;
  flex-direction: column;
  //background: linear-gradient(0deg, rgba(0, 0, 0, 0.05) 0%, rgba(31, 31, 31, 0.8) 100%);
  backdrop-filter: blur(8px);
  z-index: 1;
  .container-textarea {
    position: relative;
  }
  .container-textarea:after{
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    z-index: -2;
    padding: 3px;
    inset: -3px;
    background: linear-gradient(var(--rotaion-variable), rgba(34,32,38,1) 70%, rgba(128,0,128,1) 80%, rgba(128,0,128,1) 100%) !important;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    transition: all 0.5s ease-in-out;
  }
}
.base-chat.chat-on-error {
  .container-textarea:after {
    background: linear-gradient(var(--rotaion-variable), rgba(34,32,38,1) 70%, rgba(241,2,1,1) 80%, rgba(241,2,1,1) 100%) !important;
  }
}

.first-prompt .base-chat {
  -webkit-backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(10px);
  border-radius: 0.75rem !important;
  border: 1px solid rgba(255, 255, 255, 0.05);

  .show-prompt {
    background-color: rgba(255, 255, 255, 0.03);
    border-radius: 0.75rem;

    &:after, &:before {
      content: "";
      position: absolute;
      inset: 0;
      border-radius: inherit;
      z-index: 1;
      padding: 1px;
      pointer-events: none;
      transition: all 0.5s;
      -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      mask-composite: exclude;
    }

    &:after {
      background: linear-gradient(
        125deg,
        transparent 0%,
        rgba(34, 32, 38, 0) 90%,
        #86F49C 100%
      );
    }

    &:before {
      padding:1px;
      background: linear-gradient(
        300deg, /* roughly opposite angle */
        transparent 0%,
        rgba(34, 32, 38, 0) 90%,
        #86F49C 100%,

      );
    }

    &--secondary {
      background-color: #111829CC;
    }
  }
}
.line {
  transition: all .3s ease-in-out;
  display: flex;
  height: 100%;
  flex-wrap: nowrap;
}
.show-multi-lines .line {
  flex-wrap: wrap;
}

.landing-page {
  position: relative;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 0.75rem;
  z-index: 1;

  &:before {
    content: '';
    position: absolute;
    width: 240px;
    height: 100px;
    top: 0;
    left: 17.5%;
    transform: translate(-50%, -50%) rotate(10deg);
    background: radial-gradient(ellipse at center, #86F49C 0%, transparent 80%);
    opacity: 0.2;
    z-index: -1;
    pointer-events: none;
    filter: blur(18px);
  }

  &:after {
    content: '';
    position: absolute;
    width: 500px;
    height: 200px;
    top: 0;
    left: 35%;
    transform: translate(-50%, -50%) rotate(10deg);
    background: radial-gradient(ellipse at center, #86F49C 0%, transparent 80%);
    opacity: 0.2;
    z-index: -1;
    pointer-events: none;
    filter: blur(18px);
  }

  @media (max-width: 768px) {
    &:before,
    &:after {
      left: 65%;
      width: 600px;
      height: 250px;
    }

    border-radius: 0;
  }
}

.base-chat {
  max-width: 1005px;
  display: flex;
  flex-direction: column;
  background-color: #121724;
  z-index: 1;

  .container-textarea {
    position: relative;
  }
  .container-textarea:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    z-index: -1;
    padding: 1px;
    inset: -1px;
    background: linear-gradient(var(--rotaion-variable), rgba(34,32,38,1) 70%, rgba(134,244,156,1) 80%, rgba(134,244,156,1) 100%);
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    transition: all 0.5s ease-in-out;
  }
  .container-chat-bg {
    max-height: 280px;
  }
  .container-chat-bg.hide-prompt {
    max-height: 60px;
    overflow: hidden;

    .top-header-prompt {
      padding-bottom: 0 !important;
      border-bottom: unset !important;
    }

    .prompt-actions-button,
    textarea {
      height: 0 !important;
      padding: 0 !important;
      min-height: unset !important;
      opacity: 0;
      max-height: unset !important;
      overflow: hidden;
    }
  }

  @media screen and (max-width: 419px) {
    .container-chat-bg.hide-prompt {
      max-height: 90px !important;
    }
  }

  textarea {
    color: #FFF;
    font-family: 'Manrope', sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;

    &::placeholder {
      font-family: 'Manrope', sans-serif;
      font-size: 14px;
      color: rgb(255 255 255 / 0.7);
      font-style: normal;
      font-weight: 300;
      line-height: normal;
    }
  }
}

.tooltip {
  position: absolute;
  bottom: 130%;
  left: 50%;
  transform: translateX(-50%) translateY(4px);
  padding: 6px 8px;
  background: rgba(26, 31, 46, 0.95);
  border: 1px solid rgba(74, 222, 128, 0.1);
  border-radius: 6px;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}


.action-button {
  position: relative;
  transition: all 0.2s ease;
  border-radius: 6px;
  background: transparent;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 6px;
    padding: 1px;
    background: linear-gradient(
        45deg,
        rgba(74, 222, 128, 0) 0%,
        rgba(74, 222, 128, 0.3) 50%,
        rgba(74, 222, 128, 0) 100%
    );
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover::before {
    opacity: 1;
  }

  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.user-message p {
  margin: 0;
}
.action-button:hover .tooltip {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}
@media(max-width: 1400px) {
  .aligned-titles {
    flex-direction: column;
    align-items: flex-start;
  }
}

.send-btn-chat {
  border-radius: 12px;
  box-shadow: 0 0 4px 0 #4ADE80;

  img {
    width: 16px;
  }
}
.leading-relaxed {
  line-height: 1.625;
  letter-spacing: 0.4px;
  font-weight: 200 !important;
}
.copy-border {
  border-left: none;
  border-right: none;
  border-bottom: none;
}
.user-copy-border {
  border: 1px solid #161b28 !important;
}
.main-top-flex {
  padding-bottom: 0 !important;
}
.settings-icons:hover {
  border-radius: 8px;
}
.chat-style-apply {
  border: unset !important;

  .entire-chat {
    padding-top: 0;
    width: var(--chat-min-width, 519px);
    margin-left: 1rem !important;
    flex-grow: 0 !important;
    overflow: hidden;
  }
  @media screen and (max-width: 800px){
    .entire-chat {
      width: 100%;
    }
  }
  .bg-chat-biela {
    border-radius: 13px;
    backdrop-filter: blur(8px);
    z-index: 1;
    position: relative;
    background-position: bottom;
    background-size: calc(100% + 0px) calc(100% + 2px);
    background-repeat: no-repeat;
    .user-message .copy-border {
      border-top: 1px solid #121624;
      border-radius: 0 0 12px 12px;
      align-items: center;
      margin: 0;
    }
    .user-message .main-top-flex {
      padding-bottom: 0.75rem !important;
    }
    .special-bg.user-message {
      background-color: #121624;
      padding: 0;
    }

    .special-bg {
      border: 1px solid #4ade801a;
      font-size: 14px !important;
      display: flex;
      gap: 16px;
      border-radius: 11px;
      background: #0A0F1C;
      height: calc(100% - 2px);
      overflow: hidden;
      width: var(--chat-min-width);
    }
    @media screen and (max-width: 800px){
      .special-bg {
        width: 100%;
      }
    }

    li {
      font-size: 14px;
    }
    .spinner {
      background-color: #4ADE80 !important;
    }

    p {
      font-family: 'Manrope', 'sans-serif';
    }

    &:before {
      content: '';
      position: absolute;
      top: -1px;
      left: 0;
      right: 0;
      height: 100%;
      max-height: 300px;
      border-radius: 8px;
      //background-image: radial-gradient(circle, #4ADE80, #4ADE80, #4ADE80, #36493a, #000000);
      background-size: calc(100% + 0px) calc(100% + 0.5px);
      z-index: -1;
    }
  }

  @media screen and (max-width: 800px) {
    .bg-chat-biela {
      max-width: unset;
    }
  }
  &:has(.hide-prompt) {
    .flex-messages div[class^="react-scroll-to-"] {
      padding-bottom: 240px;

    }
  }

  @media screen and (max-height: 419px) {
    &:has(.hide-prompt) {
      .flex-messages div[class^="react-scroll-to-"] {
        padding-bottom: 190px;
      }
    }
  }

  .flex-messages div[class^="react-scroll-to-"] {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding-bottom: 240px;
    padding-right: calc(var(--workbench-width) + 13px);
    overflow-y: scroll !important;
    max-height: calc(100% );
    position: relative;
  }
  .roadmaps-history-tabs div[class^="react-scroll-to-"] {
    max-height: calc(100vh - 250px);
    border-left: 1px solid rgb(255 255 255 / 0.05);
    position: relative;
    padding-top: 15px;


  }
  .history-scroll  div[class^="react-scroll-to-"] {
    padding-left: 15px;
    padding-top: 15px;
  }
  .roadmaps-history-tabs:after {
    content: '';
    position: absolute;
    top: 0;
    left: calc(var(--chat-min-width) - 2px);
    height: 100%;
    width: 1px;
    background-color: rgb(255 255 255 / 0.05);
  }
  .roadmaps-history-tabs div[class^="react-scroll-to-"]:before {
    height: 15px !important;
    top: 198px !important;


  }
  @media screen and (max-width: 800px) {
    .flex-messages div[class^="react-scroll-to-"]:before {
      width: calc(100% - 76px);
    }
  }

  .height-write {
    height: 84px !important;
  }

  .padding-border-chat {
    padding-left: 24px;
    overflow-y: auto;
  }

  .header-chat-area {
    display: flex;
    justify-content: space-between;
  }

  .title-project {
    color: #D5D5D5;
    font-family: 'Manrope', 'sans-serif';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }

  .chat-textarea-font::placeholder {
    color: rgb(255 255 255 / 0.7);
    font-family: 'Manrope', 'sans-serif';
    font-size: 14px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
  }

  .edit-button {
    background-color: unset;
  }

  .create-text {
    font-family: 'Manrope', 'sans-serif';
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .pack-font {
    color: #FFF;
    font-family: "Fira Code", 'sans-serif' !important;
    font-size: 12px !important;
    font-style: normal;
    font-weight: 300;
    line-height: 24px;
  }

  .gap-messages {
    float: left;
    overflow-y: hidden;
    gap: 16px;
  }

  .flex-messages {
    display: flex;
    flex-direction: column;
    float: left;
    overflow-y: scroll;
    gap: 16px;
    background: #0A0F1C;

  }
  .slider {
    .bg-biela-elements-item-backgroundAccent {
      background-color: #4ADE80;

      img {
        filter: brightness(0) saturate(100%) invert(0%) sepia(91%) saturate(32%) hue-rotate(60deg) brightness(85%) contrast(99%);
      }
    }

    .text-biela-elements-item-contentAccent {
      color: #010101;
      text-align: center;
      font-family: 'Manrope', 'sans-serif';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      padding: 8px 12px;

      img {
        filter: brightness(0) saturate(100%) invert(0%) sepia(91%) saturate(32%) hue-rotate(60deg) brightness(85%) contrast(99%);
      }
    }

    .text-biela-elements-item-contentDefault {
      color: #FFF;
      text-align: center;
      font-family: 'Manrope', 'sans-serif';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      padding: 8px 12px;

      img {
        filter: brightness(0) saturate(100%) invert(100%) sepia(6%) saturate(0%) hue-rotate(198deg) brightness(105%) contrast(101%);
      }
    }
  }

  .right-part-btn {
    .flex.overflow-y-auto {
      display: flex;
      gap: 12px;
    }
  }

  .drop-select-modular {
    background-color: unset;
    color: #D5D5D5;
    font-family: 'Manrope', 'sans-serif';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    gap: 10px;
  }

  .middle-edit {
    position: absolute;
    left: 40%;
  }

  .edit-input {
    background-color: unset;
    border: unset;
    font-family: 'Manrope', 'sans-serif';
    font-size: 14px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    color: #fff;

    &:focus {
      outline: unset;
    }
  }

  .tokens-used {
    color: #FFF;
    font-family: 'Manrope', 'sans-serif';
    font-size: 8px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    display: flex;
    gap: 4px;
  }

  .header-work-space {
    display: flex;
    justify-content: space-between;
    padding: 12px;
    align-items: center;
    position: relative;
    border-bottom: 1px solid #2E2E2E;
  }

  .absolute-config-btn {
    position: absolute;
    bottom: -18px;
    /* Turn this parent into a "box" */
    display: inline-flex;
    justify-content: flex-end;
    align-items: center;

    /* Give it a background, border, and padding */
    background: #0A0F1C;
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 0 0 18px 18px;
    padding: 6px 12px;
    width: 93.5%;
    font-family: Manrope;
  }

  .configuration-btn {
    display: flex;
    gap: 8px;
    background: #0A0F1C;
    align-items: center;
    span {
      font-family: 'Manrope', 'sans-serif';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      color: #83878d;
    }
  }

  .download-btn {
    display: flex;
    gap: 10px;
    border-radius: 500px;
    background-color: #282828;
    padding: 12px 24px;
    color: #FFF;
    font-family: 'Manrope', 'sans-serif';
    font-size: 14px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    align-items: center;

    img {
      width: 14px;
    }
  }

  .send-btn-chat {
    border-radius: 12px;
    box-shadow: 0 0 4px 0 #4ADE80;

    img {
      width: 16px;
    }
  }

  .deploy-btn {
    display: flex;
    gap: 10px;
    border-radius: 500px;
    background-color: #4ADE80;
    padding: 12px 24px;
    color: #010101;
    font-family: 'Manrope', 'sans-serif';
    font-size: 14px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    align-items: center;

    img {
      width: 14px;
    }
  }

  .code-preview-slider {
    width: fit-content;
    padding: 6px 4px;
  }

  .workbench-height {
    margin-top: -8px;
    margin-bottom: 7px;
  }


  .copy-important {
    height: calc(100% - 25px);
  }

  .copyright-teachme {
    color: #FFF;
    text-align: center;
    font-family: 'Manrope', 'sans-serif';
    align-items: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    padding-top: 16px;
    margin-bottom: 24px;
  }
  .resize-handle {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    cursor: ns-resize;
    background: transparent;
    transition: background-color 0.2s ease;
  }

  .resize-handle:hover:before {
    content: '';
    height: 1px;
    width: 100%;
    display: block;
    background: linear-gradient(90deg,#00000000,#4ade80,#4ade80,#4ade80,#00000000);
  }

  .base-chat {
    width: auto;
    position: relative;
    margin-top: 0;
    margin-right: 0;
    margin-bottom: 0;
    z-index: 40;
    margin-left: 0rem;
    --max-width: var(--chat-min-width);
    display: flex;
    flex-direction: column;
    //background: linear-gradient(0deg, rgba(0, 0, 0, 1) 0%, rgba(31, 31, 31, 1) 100%);
    //backdrop-filter: blur(8px);
    z-index: 1;
    border-radius: 0 0 0.5rem 0.5rem;
    min-height: 200px;
    @media screen and (max-width: 800px){
      .base-chat{
        max-width: unset;
      }
    }

    .show-prompt {
      border-radius: 0 0 0.5rem 0.5rem;
    }
  }
  .message-content-title {
    display: none;
  }
  .no-artefact {
    .message-content-title {
      display: block;
    }
  }
  .has-artefact > p,
  .has-artefact > ul,
  .has-artefact > ol,
  .has-artefact > a,
  .has-artefact > li {
    display: none;
  }
  .has-artefact .artifact,
  .has-artefact .artifact button > div {
    margin: 0 !important;
  }
  ._MarkdownContent_t30fa_1 {
    border: 1px solid rgb(255 255 255 / 0.05);
    border-bottom: 0;
    border-radius: 12px 12px 0 0;
    padding: 1rem;
  }
  .base-response {
    width: 100%;
    margin: 0;
    max-width: unset;
  }
  .base-response:after {
    content: "";
    position: absolute;
    width: 2px;
    background: rgba(255, 255, 255, 0.05);
    top: 96px;
    left: calc(100% - 2px);
    height: calc(100% - 20%);
    z-index: -1;
  }

  .link-copy input {
    color: #D5D5D5;
    font-family: 'Manrope', 'sans-serif';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    padding-right: 20px;
  }

  .link-copy {
    width: 100%;
  }

  .label-border {
    position: absolute;
    width: 96%;
    bottom: calc(100% + 1px);
    border-radius: 8px 8px 0 0;
    border: 1px solid #2D2D2D;
    background: #111;
    color: #FFF;
    font-family: 'Manrope', 'sans-serif';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    text-transform: capitalize;
    padding: 12px 8px;
    display: flex;
    justify-content: space-between;
    gap: 4px;
    align-items: center;

    img {
      width: 14px;
    }
  }

  .link-area-container {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    border-top: 1px solid #2E2E2E;
    border-bottom: 1px solid #2E2E2E;
    background: linear-gradient(180deg, #010101 57.32%, #161616 100%);
  }

  .website-link {
    width: 100%;
    display: flex;
    gap: 10px;
    align-items: center;

    img {
      max-width: 16px;
    }
  }

  .editor-fonts div {
    color: #FFF;
    font-family: 'Manrope', 'sans-serif';
    font-size: 14px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    gap: 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  .editor-fonts div::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
  .right-btn-coding {
    display: flex;
    gap: 4px;

    button {
      background-color: transparent;
    }
  }

  .terminal-pills button {
    color: #FFF;
    font-family: 'Manrope', 'sans-serif';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  .text-biela-elements-textTertiary {
    color: #777;
    font-family: 'Manrope', 'sans-serif';
    font-size: 14px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
  }

  .text-biela-elements-textPrimary {
    color: #FFF;
    font-family: 'Manrope', sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 300;
    line-height: 24px;
  }

  .flex.gap-\[24px\].items-center {
    .button {
      outline: unset;
    }
  }

  .text-sm.h-full.overflow-y-auto {
    .truncate.w-full.text-left, .flex-1.truncate.pr-2 {
      color: #777;
      font-family: 'Manrope', 'sans-serif';
      font-size: 14px;
      font-style: normal;
      font-weight: 300;
      line-height: normal;
    }
  }

  .text-sm.h-full.overflow-y-auto {
    button {
      gap: 4px !important;
    }
  }

  .green-light {
    width: 80px;
    height: 10px;
    top: -28px;
    border-radius: 50%;
    background-color: #28FF7A;
    position: absolute;
    filter: blur(10px);
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
  }

  .flex.flex-col.w-full.flex-1.max-w-chat.pb-6.mx-auto.z-1.gap-messages.flex.flex-col.overflow-y-auto {
    justify-content: flex-end;
    overflow-y: scroll !important;
  }

  .light-left {
    left: 37%;
  }

  .light-right {
    left: 30%;
  }

  .pulsing {
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.3);
      opacity: 0.7;
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
  @keyframes pulseDot {
    0% { transform: scale(0.8); opacity: 0.6; }
    50% { transform: scale(1.2); opacity: 1; }
    100% { transform: scale(0.8); opacity: 0.6; }
  }
  .notification-dot {
    animation: pulseDot 2s infinite ease-in-out;
  }

  .bg-biela-elements-item-backgroundAccent {
    background-color: #303832;
  }

  @media(max-width: 1300px) {
    .header-work-space {
      flex-wrap: wrap;
      gap: 20px;
    }
    .middle-edit {
      position: unset;
    }
  }
}
@keyframes pulse-retry-button {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--pulse-color, 74, 222, 128), 0.3);
    color: rgb(229, 231, 235);
  }
  70% {
    box-shadow: 0 0 25px 15px rgba(var(--pulse-color, 74, 222, 128), 0.1);
    color: rgba(74, 222, 128, 1);
  }
  100% {
    box-shadow: 0 0 15px 0 rgba(var(--pulse-color, 74, 222, 128), 0);
    color: rgba(74, 222, 128, 0.7);
  }
}


.pulse-retry-button {
  animation: pulse-retry-button 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  pointer-events: none;
}
.chat-default {
  margin-bottom: 20px;
  .flex.items-center.flex-1.gap-1.show-prompt.rounded-md.overflow-hidden.border-0 {
    padding: 4px;
  }
  .custom-button {
    transition: all 300ms ease-in-out;
  }
}
.chat-style-apply-chat {
  @extend .chat-style-apply;
  &:has(.hide-prompt) {
    .flex-messages div[class^="react-scroll-to-"] {
      padding-bottom: 280px;
    }
  }
}

@media screen and (max-width: 840px) {
  .aligned-menu-header {
    gap: 0;
  }
}
@media screen and (max-width: 800px) {
  .padding-border-chat {
    padding: 0 25px;
  }
  .chat-style-apply .padding-border-chat {
    padding: 0 0 0 24px;
  }
  .chat-style-apply .flex-messages div[class^=react-scroll-to-] {
    padding-right: 65px;
  }
  .chat-style-apply:has(.z-workbench) .base-response {
    padding-right: 20px;
  }
  .chat-style-apply:not(:has(.z-workbench.open)) .base-chat {
    right: 25px;
    left: 25px;
    max-width: unset;
  }
  .chat-style-apply:has(.z-workbench.open) .base-response {
    padding-right: 55px;
  }
  .chat-style-apply:has(.z-workbench.open) .base-chat {
    right: 55px;
    left: 0;
    max-width: calc(100vw - 54px);
  }
  .chat-style-apply .flex-messages div[class^=react-scroll-to-] {
    max-height: unset;
    padding-bottom: 240px !important;
  }
  .chat-style-apply .roadmaps-history-tabs:after,
  .chat-style-apply-chat .roadmaps-history-tabs:after,
  .chat-style-apply .base-response:after {
    right: 54px;
    left: unset;
  }
}

.base-chat .container-textarea.dragging::after,
.chat-mode .container-textarea.dragging::after {
  opacity: 0 !important;
  background: none !important;
  transition: opacity .15s ease;
}

.long-thin-dashed {
  border: 1px dashed white;
  border-radius: 10px;
}

.box {
  background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='9' ry='9' stroke='white' stroke-width='2' stroke-dasharray='20%2c8' stroke-dashoffset='51' stroke-linecap='square'/%3e%3c/svg%3e");
  border-radius: 9px;

}


@keyframes borderAnimation {
  from { background-position: 0 0, -54.27px 0, 100% -54.27px, 0 100%; }
  to { background-position: 0 -54.27px, 0 0, 100% 0, -54.27px 100%; }
}

// Custom 2px green scrollbar for the whole page

body > * ::-webkit-scrollbar {
  width: 2px !important;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

}


body > * ::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05) !important;
}

body > * ::-webkit-scrollbar-thumb {
  width: 2px !important;
  background: linear-gradient(#00000000,#00000000,#4ade80,#4ade80,#4ade80,#00000000,#00000000) !important;
  min-height: 150px;
  cursor: grab;
  transition: all 0.2s ease-in-out;
}

body > * ::-webkit-scrollbar-thumb:active {
  cursor: grabbing;

}


 .horizontal-scroll::-webkit-scrollbar-thumb {
  background: linear-gradient(
    to right,
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 0),
    #4ade80,
    #4ade80,
    #4ade80,
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 0)
  ) !important;
}
.horizontal-scroll::-webkit-scrollbar {
  height: 2px !important;
}

@supports not selector(::-webkit-scrollbar) {
  body > * {
    scrollbar-color: #4ade80 transparent !important;
  }
}


.bg-gradient-top-green {
  background: linear-gradient(180deg, #000000 0%, #000000 50%, #000000 100%);
}
