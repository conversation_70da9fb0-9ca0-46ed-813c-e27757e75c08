import React, { ReactNode, UIEvent, useEffect, useState } from 'react';
import { Header } from '~/components/common/Header/Header';
import CopyRight from '~/components/common/Footer/copyRight';
import ClientOnly from '~/components/common/ClientOnly';
import '~/components/styles/index.scss?url';
import LoginModal from '~/backOffice/components/auth/LoginModal';
import { useLocation } from 'react-router-dom';
import { useUser } from '~/ai/lib/context/userContext';

interface WrapperProps {
  children: ReactNode;
  handleSendMessage?: (event: UIEvent, messageInput?: string) => void;
  isStreaming?: boolean;
  isDashboardPage?: boolean;
  isSettingsPage?: boolean;
  isProfilePage?: boolean;
  handleStop?: () => void;
  footerOff?: boolean;
  chatStarted?: boolean;
}
const Wrapper: React.FC<WrapperProps> = ({
  children,
  handleSendMessage,
  isStreaming,
  isDashboardPage,
  isSettingsPage,
  isProfilePage,
  handleStop,
  footerOff,
  chatStarted,
}) => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const { isLoggedIn } = useUser();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const [confirmPasswordToken, setConfirmPasswordToken] = useState<string | null>(
    searchParams.get('confirm-password-token')
  );
  const ref = searchParams.get('referral');
  const coupon = searchParams.get('coupon');
  const [redirectUrl, setRedirectUrl] = useState<string | null>(searchParams.get('redirectUrl'));
  const [signInModal, setSignInParams] = useState<string | null>(searchParams.get('login'));
  const [registerModal, setRegisterParam] = useState<string | null>(searchParams.get('register'));

  useEffect(() => {
    if (isLoggedIn()) {
      return;
    }
    if (signInModal) {
      setIsLoginModalOpen(true);
      setIsRegisterModalOpen(false);
    } else if (registerModal) {
      setIsLoginModalOpen(true);
      setIsRegisterModalOpen(true);
    } else if (confirmPasswordToken || redirectUrl) {
      setIsLoginModalOpen(true);
      setIsRegisterModalOpen(false);
    } else if (ref || coupon) {
      setIsLoginModalOpen(true);
      setIsRegisterModalOpen(true);
    }
  }, [searchParams]);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
      setIsScrolled(scrollTop > 0);
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);
  return (
    <div className={`flex flex-col min-h-screen ${footerOff ? 'overflow-hidden max-h-[100vh]' : ''}`}>
      <ClientOnly>
        {() => (
          <div className={`fixed xl:h-20 top-0 left-0 right-0 z-50 transition-all duration-300 bg-[#0A1730]`}>
            <Header
              handleSendMessage={handleSendMessage}
              isStreaming={isStreaming}
              isDashboardPage={isDashboardPage}
              isProfilePage={isProfilePage}
              isSettingsPage={isSettingsPage}
              handleStop={handleStop}
              chatStarted={chatStarted}
              setIsLoginModalOpen={setIsLoginModalOpen}
              setIsRegisterModalOpen={setIsRegisterModalOpen}
            />
          </div>
        )}
      </ClientOnly>
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => {
          setIsLoginModalOpen(false);
          setIsRegisterModalOpen(false);

          const searchParams = new URLSearchParams(window.location.search);
          searchParams.delete('confirm-password-token');
          searchParams.delete('login');
          searchParams.delete('register');
          searchParams.delete('redirectUrl');

          const newUrl = `${window.location.pathname}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
          window.history.replaceState({}, '', newUrl);
          setConfirmPasswordToken('');
          setSignInParams(null);
          setRegisterParam(null);
          setRedirectUrl(null);
        }}
        isRegister={isRegisterModalOpen}
        redirectUrl={redirectUrl}
        confirmPasswordToken={confirmPasswordToken}
      />
      <main className="flex-1 ">{children}</main>
      {!footerOff && (
        <div className={'bg-[#0a1730] flex'}>
          <CopyRight />
        </div>
      )}
    </div>
  );
};

export default Wrapper;
