import React, { ReactElement } from 'react';
import { ValueType } from '~/backOffice/components/billings/components/helper';

export interface Feature {
  name: string;
  description: string;
}

export interface Plan {
  id: string;
  name: string;
  price: number;
  stripeProductId: string;
  stripePriceId: string;
  tokenAllocation: string;
  period: string;
  features: string[];
  colorFrom: string;
  colorTo: string;
  icon: string;
  isPopular: boolean;
  currentPlan: boolean;
  description?: string;
}

export interface PlanCardProps {
  plan: Plan;
  index: number;
}

export interface PlanCardSkeletonProps {
  index: number;
}

export interface Invoice {
  id: string;
  created: number;
  description: string;
  amount_paid: number;
  status: string;
  invoiceNumber?: string;
}

export interface InvoiceData {
  invoiceNumber: string;
  [key: string]: any;
}

export interface TopUp {
  rate: number;
  volumeDiscounts: Array<{
    threshold: string;
    discount: number;
  }>;
  bulkPurchase: {
    minimum: string;
    maximum: string;
  };
}

export interface CheckoutSessionMode {
  mode: 'payment' | 'subscription';
  planId?: string;
  priceId?: string;
  paymentAmount?: string;
  tokens?: string;
  couponCode?: string;
}

export interface TokenCalculatorProps {
  showTokenCalculator: boolean;
  setShowTokenCalculator: (show: boolean) => void;
  activeTopUp: TopUp | null;
  onPurchase: (price: number) => Promise<void>;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    currentPage: number;
    pageSize: number;
    totalPages: number;
  };
}

export interface Invoice {
  id: string;
  created: number;
  description: string;
  amount_paid: number;
  status: string;
  type: 'subscription' | 'topup' | 'crypto';
  paymentId?: string;
}

export interface PaymentMethodProps {
  method: 'card' | 'paypal' | 'crypto';
  isSelected: boolean;
  isDisabled?: boolean;
  onClick: () => void;
}

export interface BillingToggleProps {
  stripePlans: Plan[];
  billingCycle: 'monthly' | 'yearly';
  setBillingCycle: (cycle: 'monthly' | 'yearly') => void;
  onShowTokenPurchase?: () => void;
  hideBuyMoreTokensButton?: boolean;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalRecords: number;
  pageSize: number;
  handlePageChange: (page: number) => void;
  renderPaginationItems: () => React.ReactNode;
  loadingInvoices: boolean;
}

export interface PlansListProps {
  plans: Plan[];
  loading: boolean;
  isMobile: boolean;
  billingCycle: 'monthly' | 'yearly';
}

export interface RecentInvoicesProps {
  setSelectedInvoice: (invoice: InvoiceData | null) => void;
  setShowInvoiceModal: (show: boolean) => void;
  setNotLoadedInvoices: (loaded: boolean) => void;
}

export interface WalletData {
  address: string;
  currency: string;
  processingFee: number;
}

export interface Currency {
  label: string;
  value: string;
  icon: string;
}

export interface CryptoTokensProps {
  onBack: () => void;
}

export interface PlanCardProps {
  plan: Plan;
  index: number;
  billingCycle: 'monthly' | 'yearly';
  onSubscribe: (planId: string, priceId: string) => Promise<void>;
  isMobile: boolean;
  appliedCoupon?: {
    code: string;
    value: number;
    valueType: ValueType;
    isValid: boolean;
  } | null;
}

export interface ReceiptData {
  date: string;
  planName: string;
  amount: string;
  tokens: string;
  paymentId: string;
  currency: string;
  couponUsed?: string;
}

export interface OrderDetailsProps {
  receiptData: ReceiptData;
}

export interface ThankYouModalProps {
  isOpen?: boolean;
  onClose: () => void;
  receiptData: ReceiptData;
}

export interface UsePaymentFlowProps {
  loadingTopups: boolean;
  plansLoading: boolean;
  notLoadedInvoices: boolean;
}

export interface ICouponProps {
  isValid: boolean;
  message: string;
  value: number;
  valueType: ValueType;
}
