// ConfirmationDialog.tsx
import React, { FC, UIEvent, ReactNode } from 'react'
import { useTranslation } from 'react-i18next'
import _ from 'lodash'

import {
  Dialog,
  DialogClose,
  DialogButton,
  DialogDescription,
  DialogTitle,
} from './Dialog'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { LoadingOverlay } from '~/ai/components/LoadingOverlay'

export enum DialogConfirmationType {
  DELETE = 'delete',
  DUPLICATE = 'duplicate',
  DOWNLOAD = 'download',
  EXPORT = 'export',
  FORK = 'fork',
  ROLLBACK = 'rollback',
  SAVE = 'save',
  RESTORE = 'restore',
}

// which types should get the "Chat" title vs the "Message" title
const chatTypes: DialogConfirmationType[] = [
  DialogConfirmationType.FORK,
  DialogConfirmationType.DELETE,
  DialogConfirmationType.SAVE,
]

interface ConfirmationDialogProps {
  isOpen: boolean
  type: DialogConfirmationType
  description: string
  onConfirm: (event: UIEvent) => Promise<void> | void
  onCancel: () => void
  isLoading?: boolean
  content?: ReactNode
  containerClassName?: string
}
export const emptyConfirmationDialogHandler = Object.fromEntries(
  Object.values(DialogConfirmationType).map((type) => [type, () => {}])
) as Record<DialogConfirmationType, () => void>;

export const ConfirmationDialog: FC<ConfirmationDialogProps> = ({
    isOpen,
    type,
    description,
    onConfirm,
    onCancel,
    isLoading = false,
    content,
    containerClassName,
  }) => {
  const { t } = useTranslation('translation')

  // e.g. "deleteButton" → "Delete"
  const typeLabel = t(`${type}Button`)
  // e.g. "deleteInfinitive" → "delete"
  const typeInfinitive = t(`${type}Infinitive`)
  // e.g. "project" → "Project"
  const nounCapitalized = _.capitalize(t(`${type}Noun`))

  return (
    <Dialog
      hasRoot
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) onCancel()
      }}
      onBackdrop={onCancel}
      className={containerClassName}
    >
      {/* close-icon in the corner */}
      <DialogClose asChild>
        <button
          className="absolute top-5 right-5 p-1 rounded hover:bg-white/10 p-2 rounded-full hover:bg-white/5 transition-colors bg-transparent"
          aria-label="Close"
        >
          <XMarkIcon className="w-5 h-5 text-white/70" />
        </button>
      </DialogClose>

      {/* title */}
      <DialogTitle>
        {t(
          chatTypes.includes(type) ? 'titleChat' : 'titleMessage',
          { typeCapitalized: nounCapitalized }
        )}
      </DialogTitle>

      <DialogDescription>
        <div className={'p-6 b-top border-t border-b border-white/10'}>
          <p>{t('dialogDescriptionText', { type: typeInfinitive, description })}</p>
          {content}
          <p className="mt-2">{t('confirmText', { type: typeInfinitive })}</p>
        </div>
      </DialogDescription>

      <div className="p-6 bg-bolt-elements-background-depth-2 flex justify-end gap-2">
        <DialogButton type="secondary" onClick={onCancel}>
          {t('cancelButton', 'Cancel')}
        </DialogButton>

        <DialogButton
          type={type === DialogConfirmationType.DUPLICATE ? 'duplicate' : 'danger'}
          onClick={onConfirm}
          disabled={isLoading}
        >
          <>
            {typeLabel}
            {isLoading && (
              <LoadingOverlay
                message={t(
                  chatTypes.includes(type)
                    ? 'loadingOverlayChat'
                    : 'loadingOverlayMessages',
                  { type: typeInfinitive }
                )}
              />
            )}
          </>
        </DialogButton>
      </div>
    </Dialog>
  )
}

export default ConfirmationDialog
