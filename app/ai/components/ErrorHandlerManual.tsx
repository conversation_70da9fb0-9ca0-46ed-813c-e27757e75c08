import { memo, useEffect, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { ChevronDownIcon, ChevronUpIcon, ExclamationTriangleIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { useStore } from '@nanostores/react';
import { workbenchStore } from '../lib/stores/workbench';
import { shouldBlockAutoSend, trackError } from '~/utils/errorTracking';
import { computed } from 'nanostores';
import { ActionAlert } from '~/types/actions';

function reducedMessage(content: string): string {
  const lines = content.split('\n');
  return lines.slice(0, 8).join('\n');
}

const ErrorHandler = memo(
  ({
    onAskBiela,
    onDiscussProblem,
    onIgnore,
  }: {
    onAskBiela: (e: string) => void;
    onDiscussProblem: (e: string) => void;
    onIgnore: () => void;
  }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const actionAlert = useStore(workbenchStore.actionAlert);

    const computedMessage = computed(workbenchStore.actionAlert, (value) =>
      value
        ? { content: reducedMessage(value.content), description: reducedMessage(value.description) }
        : { content: '', description: '' }
    );

    const error = useStore(computedMessage);

    useEffect(() => {
      (() => {
        if (!actionAlert?.content) {
          return;
        }

        const shouldProcess =
          actionAlert.type === 'migration' || actionAlert.type === 'preview' || actionAlert.type === 'error';

        if (!shouldProcess) {
          return;
        }

        if (shouldBlockAutoSend(error.content, actionAlert.type)) {
          return;
        }

        const shouldAutoSend = trackError(error.content, actionAlert.type);

        if (shouldAutoSend) {
          handleAskBiela(actionAlert);
          workbenchStore.actionAlert.set(undefined);
        }
      })();
    }, [actionAlert]);

    if (!actionAlert || !['migration', 'database', 'terminal', 'preview', 'code'].includes(actionAlert?.type?.trim()))
      return null;

    // Determine error type and colors based on error code/message
    const getErrorType = (errorType: string) => {
      if (errorType === 'migration') {
        return {
          type: 'migration',
          color: 'border-[#22D3EE]',
          bgColor: 'bg-[#A78BFA]/5',
          buttonColor: 'bg-[#22D3EE]',
          buttonHover: 'hover:bg-[#22D3EE]/90',
        };
      } else if (errorType === 'terminal') {
        return {
          type: 'terminal',
          color: 'border-[#F59E0B]',
          bgColor: 'bg-[#F59E0B]/5',
          buttonColor: 'bg-[#F59E0B]',
          buttonHover: 'hover:bg-[#F59E0B]/90',
        };
      } else {
        return {
          type: 'preview',
          color: 'border-[#EF4444]',
          bgColor: 'bg-[#EF4444]/5',
          buttonColor: 'bg-[#EF4444]',
          buttonHover: 'hover:bg-[#EF4444]/90',
        };
      }
    };

    const errorType = getErrorType(actionAlert.type);

    function handleAskBiela(actionAlert: ActionAlert) {
      const message = `*Fix this ${actionAlert.type} error* \n\`\`\`\n${error.content}\n\`\`\`\n`;
      onAskBiela(message);
      // Don't hide the component, let parent handle the cycling
    }

    const handleDiscussProblem = () => {
      const message = `*Tell me about this ${actionAlert.type} error* \n\`\`\`\n${error.content}\n\`\`\`\n`;
      onDiscussProblem(message);
      // Don't hide the component, let parent handle the cycling
    };

    const handleIgnore = () => {
      onIgnore();
      // Don't hide the component, let parent handle the cycling
    };

    return (
      <motion.div
        key={error.description} // Add key to trigger re-animation when error changes
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
        className={` rounded-lg border ${errorType.color} ${errorType.bgColor} backdrop-blur-sm overflow-hidden`}
        style={{
          background: '#121624',
          backdropFilter: 'blur(10px)',
          border: `1px solid ${errorType.color.replace('border-', '').replace('[', '').replace(']', '')}`,
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <div className={`w-8 h-8 rounded-full ${errorType.bgColor} flex items-center justify-center`}>
              <ExclamationTriangleIcon className={`w-5 h-5 ${errorType.color.replace('border-', 'text-')}`} />
            </div>
            <div>
              <h3 className="text-white font-light text-sm">Potential problem detected.</h3>
              <p className="text-white/60 text-xs font-light">
                {errorType.type === 'database' || errorType.type === 'migration'
                  ? 'Database interaction error'
                  : errorType.type === 'terminal'
                    ? 'Terminal execution error'
                    : 'Code error'}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="flex items-center gap-2 px-3 py-1.5 bg-white/5 hover:bg-white/10 rounded-md transition-colors"
            >
              <span className="text-white/70 text-sm font-light">Show problem</span>
              {isExpanded ? (
                <ChevronUpIcon className="w-4 h-4 text-white/50" />
              ) : (
                <ChevronDownIcon className="w-4 h-4 text-white/50" />
              )}
            </button>

            <button onClick={handleIgnore} className="p-1.5 hover:bg-white/10 rounded-md transition-colors">
              <XMarkIcon className="w-4 h-4 text-white/50" />
            </button>
          </div>
        </div>

        {/* Expandable Error Details */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="border-t border-white/10"
            >
              <div className="p-4">
                <div className="bg-black/20 rounded-lg p-3 mb-4 overflow-y-auto horizontal-scroll max-h-[100px]">
                  <p className="text-white/80 text-sm font-mono font-light">{error.description}</p>
                </div>
                <p className="text-white/60 text-sm font-light">
                  Provide more info about this issue to help BIELA fix it.
                </p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Action Buttons */}
        <div className="p-4 border-t border-white/10">
          <p className="text-white/70 text-sm mb-4 font-light">
            Should we try to fix this problem, or would you prefer to discuss it first?
          </p>

          <div className="flex items-center gap-3">
            <button
              onClick={() => handleAskBiela(actionAlert)}
              className={`px-4 py-2 ${errorType.buttonColor} ${errorType.buttonHover} text-white rounded-lg transition-colors text-sm font-light`}
            >
              Ask BIELA
            </button>

            <button
              onClick={handleDiscussProblem}
              className="px-4 py-2 bg-white/10 hover:bg-white/15 text-white rounded-lg transition-colors text-sm font-light"
            >
              Discuss Problem
            </button>

            <button
              onClick={handleIgnore}
              className="px-4 py-2 bg-white/5 hover:bg-white/10 text-white/70 rounded-lg transition-colors text-sm font-light"
            >
              Ignore
            </button>
          </div>
        </div>
      </motion.div>
    );
  }
);

export default ErrorHandler;
