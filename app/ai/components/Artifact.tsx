import { useStore } from '@nanostores/react';
import { AnimatePresence, motion } from 'framer-motion';
import { computed } from 'nanostores';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import { type BundledLanguage, type BundledTheme, createHighlighter, type HighlighterGeneric } from 'shiki';
import type { ActionState } from '~/ai/lib/runtime/action-runner';
import { workbenchStore } from '~/ai/lib/stores/workbench';
import { classNames } from '~/utils/classNames';
import { cubicEasingFn } from '~/utils/easings';
import { chatStore } from '~/ai/lib/stores/chat';
import { PlayIcon } from 'lucide-react';
import { sessionStore } from '~/ai/lib/stores/session/sessionStore';
import { FileNameAutoScroll } from './FileNameAutoScroll';

const highlighterOptions = {
  langs: ['shell'],
  themes: ['light-plus', 'dark-plus'],
};

const shellHighlighter: HighlighterGeneric<BundledLanguage, BundledTheme> =
  import.meta.hot?.data.shellHighlighter ?? (await createHighlighter(highlighterOptions));

if (import.meta.hot) {
  import.meta.hot.data.shellHighlighter = shellHighlighter;
}

interface ArtifactProps {
  messageId: string;
}

export const Artifact = memo(({ messageId }: ArtifactProps) => {
  const [showActions, setShowActions] = useState(false);

  const artifacts = useStore(workbenchStore.artifacts);
  const artifact = artifacts[messageId];

  const actions = useStore(computed(artifact.runner.actions, (map) => Object.values(map)));
  const { showPrompt, isStreaming, mode } = useStore(chatStore);

  const [allActionFinished, setAllActionFinished] = useState(false);

  useEffect(() => {
    setShowActions(showPrompt);
  }, [showPrompt]);

  useEffect(() => {
    if (actions.length === 0) {
      setAllActionFinished(false);
      return;
    }

    const finished = actions.every((action) => action.status === 'complete');

    if (artifact.type === 'bundled' && allActionFinished !== finished) {
      setAllActionFinished(finished);
    }
  }, [actions]);

  useEffect(() => {
    if (showPrompt) {
      return;
    }

    if (mode !== 'code') {
      return;
    }

    if (actions.length === 0) {
      return;
    }

    if (!allActionFinished) {
      return;
    }

    if (isStreaming) {
      return;
    }

    if (workbenchStore.currentView.get() !== 'preview') {
      workbenchStore.currentView.set('preview');
    }
  }, [allActionFinished, isStreaming, mode, actions.length, showPrompt]);

  return (
    <div className="artifact flex rounded-lg flex-col overflow-hidden w-full transition-border duration-150">
      <div className="flex">
        <button className="flex items-center bg-transparent w-full overflow-hidden gap-1">
          {artifact.type == 'bundled' && (
            <>
              <div>
                {allActionFinished ? (
                  <div className={'i-ph:files-light'} style={{ fontSize: '22px' }}></div>
                ) : (
                  <div className={'i-svg-spinners:90-ring-with-bg spinner'} style={{ fontSize: '2rem' }}></div>
                )}
              </div>
              <div className="bg-biela-elements-artifacts-borderColor w-[1px]" />
            </>
          )}
          <div className="w-full text-left">
            <div className="text-sm font-extralight text-white/90 pb-2">{artifact?.title}</div>
          </div>
        </button>
      </div>
      <AnimatePresence>
        {artifact.type !== 'bundled' && showPrompt && showActions && actions.length > 0 && (
          <motion.div
            className="actions"
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: '0px' }}
            transition={{ duration: 0.15 }}
          >
            <div className="text-left">
              <ActionList actions={actions} />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
});

interface ShellCodeBlockProps {
  className?: string;
  code: string;
  runCommand?: boolean;
}

function ShellCodeBlock({ className, code, runCommand }: ShellCodeBlockProps) {
  const [expendCommand, setExpendCommand] = useState<boolean>(false);
  const clickTimeoutRef = useRef<number | null>(null);

  const handleClick = () => {
    if (!runCommand) {
      return;
    }

    if (clickTimeoutRef.current) {
      // A venit al doilea click: double-click
      clearTimeout(clickTimeoutRef.current);
      clickTimeoutRef.current = null;
      setExpendCommand((prev) => !prev);
    } else {
      // Primul click: setăm timer-ul pentru a detecta dacă vine un al doilea click
      clickTimeoutRef.current = window.setTimeout(() => {
        // Nu a venit al doilea click în intervalul de 250ms => single click
        navigator.clipboard.writeText(code).then(() => {
          if (window.showToast) {
            window.showToast({
              type: 'success',
              title: 'Success',
              message: `Copied to clipboard`,
              duration: 4000,
            });
          }

          console.log('Command copied to clipboard:', code);
        });
        clickTimeoutRef.current = null;
      }, 250);
    }
  };

  return (
    <div
      onClick={handleClick}
      className={classNames(
        'text-xs',
        className,
        expendCommand ? 'show-multi-lines' : '',
        runCommand ? 'run-command' : ''
      )}
      dangerouslySetInnerHTML={{
        __html: shellHighlighter.codeToHtml(code, {
          lang: 'shell',
          theme: 'dark-plus',
        }),
      }}
    ></div>
  );
}

interface ActionListProps {
  actions: ActionState[];
}

const actionVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

/**
 * ─────────────────────────────────────────────────────────────────────────────
 * Aici începe partea relevantă pentru “Run command” cu single-click & double-click
 * ─────────────────────────────────────────────────────────────────────────────
 */

/**
 * ActionList component with delayed status updates.
 * Updates action status to completed after 1500ms delay when action becomes complete
 */
const ActionList = memo(({ actions }: ActionListProps) => {
  const [completedActions, setCompletedActions] = useState<Set<string>>(new Set());
  const timeoutRefs = useRef<Map<string, NodeJS.Timeout>>(new Map());

  const filteredActions = useMemo(
    () =>
      actions.reduce(
        (acc, action) => {
          if (!('filePath' in action)) {
            acc.result.push(action);
          } else {
            const existing = acc.latestFileActions.get(action.filePath);

            if (existing) {
              const index = acc.result.indexOf(existing.action);
              acc.result[index] = action;
            } else {
              acc.result.push(action);
            }

            acc.latestFileActions.set(action.filePath, { action, index: acc.result.length - 1 });
          }

          return acc;
        },
        {
          result: [] as ActionState[],
          latestFileActions: new Map<string, { action: ActionState; index: number }>(),
        }
      ).result,
    [actions]
  );

  const processedActions = useMemo(
    () =>
      filteredActions.map((action) => {
        const actionKey = 'filePath' in action ? action.filePath : action.type + action.content;

        if (action.status === 'complete' && completedActions.has(actionKey)) {
          return action;
        }

        if (action.status === 'complete' && !completedActions.has(actionKey)) {
          const existingTimeout = timeoutRefs.current.get(actionKey);

          if (existingTimeout) {
            clearTimeout(existingTimeout);
          }

          const timeout = setTimeout(() => {
            setCompletedActions((prev) => new Set([...prev, actionKey]));
            timeoutRefs.current.delete(actionKey);
          }, 200);

          timeoutRefs.current.set(actionKey, timeout);

          return { ...action, status: 'running' };
        }

        if (action.status !== 'complete' && completedActions.has(actionKey)) {
          setCompletedActions((prev) => {
            const newSet = new Set(prev);
            newSet.delete(actionKey);

            return newSet;
          });
        }

        return action;
      }),
    [filteredActions, completedActions]
  );

  useEffect(() => {
    return () => {
      timeoutRefs.current.forEach((timeoutId) => clearTimeout(timeoutId));
      timeoutRefs.current.clear();
    };
  }, []);

  return (
    <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} exit={{ opacity: 0 }} transition={{ duration: 0.15 }}>
      <ul className="list-none space-y-2.5 ">
        {processedActions.map((action, idx) => {
          const { status, type, content } = action;
          const actionLabel = type === 'file' && 'mode' in action && action.mode === 'update' ? 'Update' : 'Create';

          localStorage.setItem('isBouncing', 'true');

          return (
            <motion.li
              key={idx}
              variants={actionVariants}
              initial="hidden"
              animate="visible"
              transition={{
                duration: 0.2,
                ease: cubicEasingFn,
              }}
            >
              <div className="gap-1.5 text-sm">
                <div
                  className={classNames(
                    'flex items-center gap-2 rounded-md ',
                    ['start', 'unit_testing_shell', 'shell'].includes(type)
                      ? 'flex items-center gap-2 mb-2 text-white'
                      : 'bg-white/5 px-2 py-1.5'
                  )}
                >
                  <div className={classNames('text-lg', getIconColor(status as ActionState['status']))}>
                    {status === 'running' ? (
                      <div className="i-svg-spinners:90-ring-with-bg spinner w-[16px] h-[16px]"></div>
                    ) : status === 'pending' ? (
                      <div className="i-ph:circle-duotone w-[16px] h-[16px]"></div>
                    ) : status === 'complete' ? (
                      <>
                        {type === 'start' || type === 'shell' || type === 'unit_testing_shell' ? (
                          <PlayIcon className="w-4 h-4 text-white/70" />
                        ) : (
                          <div className="i-ph:check w-[16px] h-[16px]"></div>
                        )}
                      </>
                    ) : status === 'failed' || status === 'aborted' ? (
                      <div className="i-ph:x w-[16px] h-[16px]"></div>
                    ) : null}
                  </div>

                  {type === 'file' ? (
                    <div className="create-text flex items-center gap-2 text-sm font-extralight text-white/70">
                      {actionLabel}{' '}
                      <code
                        className={classNames([
                          'bg-white/5 px-2 py-0.5 w-full overflow-hidden rounded text-xs font-mono text-white/50',
                          'hover:underline cursor-pointer',
                        ])}
                        onClick={() => 'filePath' in action && sessionStore.openFile(action.filePath)}
                      >
                        <FileNameAutoScroll>{'filePath' in action ? action.filePath : ''}</FileNameAutoScroll>
                      </code>
                    </div>
                  ) : type === 'shell' || type === 'unit_testing_shell' ? (
                    <div className="flex items-center w-full text-sm text-white/70 font-extralight cursor-pointer">
                      <span className="flex-1 font-extralight ">Run command</span>
                    </div>
                  ) : type === 'start' ? (
                    <a
                      onClick={(e) => {
                        e.preventDefault();
                        workbenchStore.currentView.set('preview');
                      }}
                      className="flex items-center w-full text-sm !text-white/70"
                    >
                      <span className="flex-1">Start Application</span>
                    </a>
                  ) : type === 'migration' ? (
                    <div className="flex items-center w-full overflow-hidden text-sm text-white/70">
                      <span className="flex-1">
                        Run migration
                        {action.migrationTitle && (
                          <>
                            : <FileNameAutoScroll>{action.migrationTitle}</FileNameAutoScroll>
                          </>
                        )}
                      </span>
                    </div>
                  ) : null}
                </div>
              </div>

              {/* Afișăm blocul cu cod doar dacă e tip shell / unit_testing_shell / start */}
              {/* DAR îl afișăm doar dacă e expandat (pentru shell) sau mereu (pentru start) */}
              {type === 'start' && <ShellCodeBlock className="mt-1" code={content} />}
              {(type === 'shell' || type === 'unit_testing_shell') && (
                <ShellCodeBlock runCommand={true} className="mt-1" code={content} />
              )}
            </motion.li>
          );
        })}
      </ul>
    </motion.div>
  );
});
ActionList.displayName = 'ActionList';

// Funcție care decide culoarea iconiței în funcție de status
function getIconColor(status: ActionState['status']) {
  switch (status) {
    case 'pending': {
      return 'text-biela-elements-textTertiary';
    }
    case 'running': {
      return 'text-biela-elements-loader-progress';
    }
    case 'complete': {
      return 'text-biela-elements-icon-success';
    }
    case 'aborted': {
      return 'text-biela-elements-textSecondary';
    }
    case 'failed': {
      return 'text-biela-elements-icon-error';
    }
    default: {
      return undefined;
    }
  }
}
