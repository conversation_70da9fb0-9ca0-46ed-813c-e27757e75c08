import { useStore } from '@nanostores/react';
import { useEffect, useRef, useState } from 'react';
import { LoadingBiela } from '~/components/LoadingBiela';
import { sessionStore } from '~/ai/lib/stores/session/sessionStore';
import { AnimatePresence, motion } from 'framer-motion';
import { TerminalTabs } from './terminal/TerminalTabs';
import { Panel, PanelGroup, PanelResizeHandle } from 'react-resizable-panels';
import { FaCheck, FaCheckCircle } from 'react-icons/fa';

const ReconnectingScreen = ({ isInitialConnection }: { isInitialConnection: boolean }) => {
  const [statusMessage, setStatusMessage] = useState('Checking container status');

  useEffect(() => {
    const messages = ['Checking container status', 'Establishing connection', 'Restoring environment', 'Almost ready'];

    let index = 0;
    const interval = setInterval(() => {
      index = (index + 1) % messages.length;
      setStatusMessage(messages[index]);
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0 }}
      className="absolute inset-0 bg-[#0a0f1c] flex items-center justify-center z-999"
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      <div className="text-center px-6 max-w-md">
        <motion.div
          className="mb-6"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: 'easeOut' }}
        >
          <motion.h2
            className="text-white text-2xl font-light tracking-wide mb-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            {isInitialConnection ? 'Connecting...' : 'Reconnecting...'}
          </motion.h2>
          <motion.p
            className="text-gray-300 text-base"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            {isInitialConnection ? 'Creating your development environment' : 'Restoring your development environment'}
          </motion.p>
        </motion.div>

        <motion.div
          className="flex items-center justify-center mb-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <motion.div
            className="flex space-x-1 mr-4"
            animate={{ opacity: [0.7, 1, 0.7] }}
            transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
          >
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="w-3 h-3 bg-green-400 rounded-full"
                animate={{
                  y: [0, -12, 0],
                  opacity: [0.4, 1, 0.4],
                }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: i * 0.2,
                  ease: 'easeInOut',
                }}
              />
            ))}
          </motion.div>
          <motion.span
            className="text-white text-xl font-light tracking-wide"
            animate={{ opacity: [0.8, 1, 0.8] }}
            transition={{ duration: 2.5, repeat: Infinity }}
          >
            {statusMessage}
          </motion.span>
        </motion.div>

        <motion.div
          className="w-80 h-1 bg-gray-700 rounded-full mx-auto relative overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
        >
          <motion.div
            className="absolute top-0 left-0 h-full rounded-full bg-gradient-to-r from-green-400 to-green-600"
            animate={{
              x: ['-100%', '250%'],
            }}
            transition={{
              x: {
                duration: 3,
                repeat: Infinity,
                ease: 'easeInOut',
                repeatType: 'loop',
              },
            }}
            style={{ width: '40%' }}
          />
        </motion.div>
      </div>
    </motion.div>
  );
};

const ReconnectScreen = ({
  onReconnect,
  isInitialConnection,
}: {
  isInitialConnection: boolean;
  onReconnect: () => void;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0 }}
      className="absolute inset-0 bg-[#0a0f1c] flex items-center justify-center z-999"
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      <div className="text-center px-6 max-w-md">
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: 'easeOut' }}
        >
          <motion.h1
            className="text-yellow-400 text-3xl font-light tracking-wide mb-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            {isInitialConnection ? 'Connection Failed' : 'Welcome back!'}
          </motion.h1>
          <motion.p
            className="text-gray-300 text-base"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            {isInitialConnection
              ? 'Unable to connect to your development container. Please try again.'
              : 'Your WebContainer was paused for inactivity.'}
          </motion.p>
        </motion.div>

        <motion.button
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          onClick={onReconnect}
          className={`px-8 py-4 rounded-lg font-light text-lg tracking-wide transition-all ${'bg-gradient-to-r from-green-600 to-green-600 hover:from-green-700 hover:to-green-700 text-white shadow-lg hover:shadow-xl'}`}
        >
          {isInitialConnection ? 'Try Connect Again' : 'Reconnect Container'}
        </motion.button>
      </div>
    </motion.div>
  );
};

const SuccessScreen = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0 }}
      className="absolute inset-0 bg-[#0a0f1c] flex items-center justify-center z-999"
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      <div className="text-center px-6 max-w-md">
        <motion.div
          className="mb-6"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, ease: 'easeOut' }}
        >
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{
              duration: 0.8,
              ease: 'easeOut',
              type: 'spring',
              stiffness: 200,
            }}
            className="text-6xl mb-6 flex justify-center"
          >
            <FaCheckCircle className={'text-[#4ADE80]'} />
          </motion.div>

          <motion.h2
            className="text-green-400 text-2xl font-light tracking-wide mb-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            Successfully Reconnected!
          </motion.h2>
          <motion.p
            className="text-gray-300 text-base"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            Your development environment is ready
          </motion.p>
        </motion.div>

        <motion.div
          className="w-80 h-1 bg-gray-700 rounded-full mx-auto relative overflow-hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          <motion.div
            className="absolute top-0 left-0 h-full rounded-full bg-gradient-to-r from-green-400 to-green-600"
            initial={{ width: 0 }}
            animate={{ width: '100%' }}
            transition={{ duration: 1.2, ease: 'easeOut' }}
          />
        </motion.div>
      </div>
    </motion.div>
  );
};

export const EditorPanel = () => {
  const [isFullyLoaded, setIsFullyLoaded] = useState(false);
  const conainerStatus = useStore(sessionStore.containerStatus);
  const isContainerActive = useStore(sessionStore.isContainerActive);
  const [retryStatus, setRetryStatus] = useState<'success' | 'connecting' | 'reconnect'>();
  const [isInitialConnection, setIsInitialConnection] = useState(true);
  useEffect(() => {
    sessionStore.connectToContainer().then((connected) => setRetryStatus(connected ? undefined : 'reconnect'));
    const unsubscribe = sessionStore.containerStatus.listen(
      (status) => status === 'connected' && setIsInitialConnection(false)
    );
    setTimeout(() => {
      setIsFullyLoaded(true);
    }, 6000);

    return () => {
      unsubscribe();
    };
  }, []);

  useEffect(() => {
    if (conainerStatus === 'connecting') {
      setRetryStatus('connecting');
    }
    if (conainerStatus === 'disconnected') {
      setRetryStatus('reconnect');
    }
  }, [conainerStatus]);
  const containerRef = useRef<HTMLDivElement>(null);
  const newChat = useRef(sessionStorage.getItem('newChat'));
  const containerHeight = containerRef.current?.offsetHeight || 0;
  const desiredHeightPx = 240;
  const fractionSize = containerHeight > 0 ? (desiredHeightPx / containerHeight) * 100 : 0;

  const handleReconnect = async () => {
    setIsInitialConnection(false);
    setRetryStatus('connecting');
    const connected = await sessionStore.connectToContainer(true);
    if (connected) {
      setRetryStatus('success');
      setTimeout(() => setRetryStatus(undefined), 2000);

      sessionStore.shellQueue.add(() => sessionStore.runTask({ command: 'npm install', override: true }));
      sessionStore.startDevServer();
      return;
    }
    setRetryStatus('reconnect');
  };
  return (
    <div ref={containerRef} className="w-full h-full relative">
      {sessionStore.sessionData && (
        <PanelGroup direction="vertical">
          {fractionSize && isContainerActive && (
            <Panel id="editor-panel" order={1} defaultSize={100 - fractionSize}>
              <iframe
                allow="hid; usb; serial; cross-origin-isolated"
                onLoad={() =>
                  setTimeout(() => {
                    setIsFullyLoaded(true);

                    sessionStorage.removeItem('newChat');
                  }, 4000)
                }
                title="Code Server"
                src={`${sessionStore.sessionData.host}${sessionStore.sessionData.editorPath}?folder=/config/workspace/${sessionStore.getSlug()}&newChat=${newChat.current}`}
                className="w-full h-full"
              />
            </Panel>
          )}
          <PanelResizeHandle />
          <TerminalTabs />
        </PanelGroup>
      )}
      <AnimatePresence mode="sync">
        {retryStatus == 'reconnect' && (
          <ReconnectScreen key="reconnect" isInitialConnection={isInitialConnection} onReconnect={handleReconnect} />
        )}
        {retryStatus == 'connecting' && (
          <ReconnectingScreen isInitialConnection={isInitialConnection} key="reconnecting" />
        )}
        {retryStatus == 'success' && <SuccessScreen key="success" />}
      </AnimatePresence>

      {!isFullyLoaded && (
        <motion.div
          initial={{ opacity: 1 }}
          animate={{ opacity: 0 }}
          exit={{ opacity: 0 }}
          className="bg-[#0a0f1c] absolute inset-0 z-999"
          transition={{ delay: 2000, duration: 1 }}
        >
          <LoadingBiela className="w-full h-full" />
        </motion.div>
      )}
    </div>
  );
};
