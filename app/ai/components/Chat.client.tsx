/*
 * @ts-nocheck
 * Preventing TS checks with files presented in the video for a better presentation.
 */
import { useStore } from '@nanostores/react';
import type { JSONValue, Message } from 'ai';
import { useChat } from 'ai/react';
import React, { memo, UIEvent, useCallback, useEffect, useRef, useState } from 'react';
import { useAnimate } from 'framer-motion';
import { useSettings } from '~/ai/lib/hooks/useSettings';
import { description, generateProjectSlug, setMessages, useChatHistory } from '~/ai/lib/persistence';
import {
  chatStore,
  setEstimationsData,
  setImageDataList,
  setIsModifiedCode,
  setIsStreaming,
  setTryToRetry,
  setUploadedFiles,
} from '~/ai/lib/stores/chat';
import { workbenchStore } from '~/ai/lib/stores/workbench';
import { AI_MODEL_STORAGE_KEY, AI_MODELS, CONTENT_PROMPT, PROMPT_COOKIE_KEY } from '~/utils/constants';
import { createScopedLogger, renderLogger } from '~/utils/logger';
import { BaseChat } from './BaseChat';
import Cookies from 'js-cookie';
import { debounce } from '~/utils/debounce';
import { useMessageParser, usePromptEnhancer, useShortcuts, useSnapScroll } from '~/ai/lib/hooks';
import { useSearchParams } from 'react-router-dom';
import { createSampler } from '~/utils/sampler';
import { getTemplates, selectStarterTemplate } from '~/utils/selectStarterTemplate';
import '~/components/styles/index.scss?url';
import ErrorTooLongDialog from '~/ai/components/ErrorToLongDialog'; // Import the dialog here
import Wrapper from '~/components/common/Wrapper';
import SessionStore from '../lib/stores/session/sessionStore';
import ErrorRunOutOfTokensDialog from '~/ai/components/ErrorRunOutOfTokensDialog';
import { createCheckoutSession } from '~/lib/stores/billing';
import { TokensStore } from '~/ai/lib/stores/tokens/tokensStore';
import { backendApiFetch } from '~/ai/lib/backend-api';
import { cleanStackTrace } from '~/utils/stacktrace';
import { resetErrorTracking } from '~/utils/errorTracking';
import { getUserPreferences } from '../lib/stores/contentStudio';
import { controlCenterStore } from '../lib/stores/controlCenter';
import { updateProject } from '~/ai/lib/stores/projects/project';
import { getProject } from '~/api/projectsApi';

type MessageWithHidden = Message & { hiddenAt?: string; isChatMsg?: boolean };

const EXTENDED_THINKING_TOKEN_THRESHOLD = Number(import.meta.env.VITE_EXTENDED_THINKING_TOKEN_THRESHOLD) || 100000;

const logger = createScopedLogger('Chat');

export function Chat({ id }: { id: string }) {
  const [projectName, setProjectName] = useState('');
  renderLogger.trace('Chat');

  const { ready, loadingFromBackend, initialMessages, storeMessageHistory, importChat, exportChat, loadChatById } =
    useChatHistory();

  const title = useStore(description);
  const lastUpdatedRef = useRef<string>(title);

  if (title && lastUpdatedRef.current !== title && !projectName) {
    updateProject(title);
    lastUpdatedRef.current = title;
  }

  useEffect(() => {
    workbenchStore.setReloadedMessages(initialMessages.map((m) => m.id));
  }, [initialMessages]);

  useEffect(() => {
    if (!id) return;

    const fetchProjectAndChat = async () => {
      const projectSlug = generateProjectSlug(id);
      if (projectSlug) {
        try {
          const project = await getProject(projectSlug);
          if (project?.projectName) {
            setProjectName(project.projectName);
          }
        } catch (error) {
          console.error('Error fetching project:', error);
        }
      }

      try {
        await loadChatById(id);
      } catch (error) {
        console.error('Error loading chat:', error);
      }
    };

    fetchProjectAndChat();
  }, [id]);

  return (
    <>
      {ready && (
        <ChatImpl
          loadingFromBackend={loadingFromBackend}
          description={title}
          initialMessages={initialMessages}
          storeMessageHistory={storeMessageHistory}
          importChat={importChat}
          exportChat={exportChat}
        />
      )}
    </>
  );
}

const processSampledMessages = createSampler(
  (options: {
    messages: Message[];
    initialMessages: Message[];
    isLoading: boolean;
    parseMessages: (messages: Message[], isLoading: boolean) => void;
    storeMessageHistory: (messages: Message[]) => Promise<void>;
  }) => {
    const validateAndFixMessages = (messages: Message[]) => {
      return messages.map((message, index) => {
        if (message.role !== 'assistant' || index === messages.length - 1) {
          return message;
        }

        let content = message.content;

        const actionOpenTags = content.match(/<bielaAction\b[^>]*>/g) || [];
        const actionCloseTags = content.match(/<\/bielaAction>/g) || [];
        const artifactOpenTags = content.match(/<bielaArtifact\b[^>]*>/g) || [];
        const artifactCloseTags = content.match(/<\/bielaArtifact>/g) || [];

        const actionOpenCount = actionOpenTags.length;
        const actionCloseCount = actionCloseTags.length;
        const artifactOpenCount = artifactOpenTags.length;
        const artifactCloseCount = artifactCloseTags.length;

        if (actionOpenCount > actionCloseCount) {
          content += '\n</bielaAction>'.repeat(actionOpenCount - actionCloseCount);

          if (artifactOpenCount > artifactCloseCount) {
            content += '\n</bielaArtifact>'.repeat(artifactOpenCount - artifactCloseCount);
          }
        }

        return {
          ...message,
          content,
        };
      });
    };

    const { messages, initialMessages, isLoading, parseMessages, storeMessageHistory } = options;
    parseMessages(validateAndFixMessages(messages), isLoading);

    if (messages.length > initialMessages.length) {
      storeMessageHistory(validateAndFixMessages(messages)).catch((error) => {
        if (window.showToast) {
          window.showToast({
            type: 'error',
            title: 'Error',
            message: error.message,
            duration: 4000,
          });
        }
        //toast.error(error.message);
      });
    }
  },
  50
);

interface PreviewError {
  type: 'error';
  message: string;
  source: string;
  lineno: string;
  colno: string;
  error: Error;
}

interface ChatProps {
  loadingFromBackend: boolean;
  initialMessages: Message[];
  storeMessageHistory: (messages: Message[]) => Promise<void>;
  importChat: (description: string, messages: Message[]) => Promise<void>;
  exportChat: (id?: string) => Promise<void>;
  description?: string;
}

export const ChatImpl = memo(
  ({ loadingFromBackend, description, initialMessages, storeMessageHistory, importChat, exportChat }: ChatProps) => {
    useShortcuts();

    const [showPromptTooLongDialog, setShowPromptTooLongDialog] = useState(false);

    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const [chatStarted, setChatStarted] = useState(!!initialMessages?.length || false);
    const uploadedFiles = useStore(chatStore).uploadedFiles;
    const imageDataList = useStore(chatStore).imageDataList;
    const [searchParams, setSearchParams] = useSearchParams();
    const [fakeLoading, setFakeLoading] = useState(false);
    const [contentPrompt, setContentPrompt] = useState(CONTENT_PROMPT);
    const [lastTotalTokensCost, setLastTotalTokensCost] = useState<number | null>(null);

    const actionAlert = useStore(workbenchStore.actionAlert);
    const { autoSelectTemplate, contextOptimizationEnabled } = useSettings();
    const { showChat, isStreaming, estimationsProject, tryToRetry } = useStore(chatStore);
    const [animationScope] = useAnimate();
    const [apiKeys, setApiKeys] = useState<Record<string, string>>({});
    const { enhancingPrompt, promptEnhanced, enhancePrompt, resetEnhancer } = usePromptEnhancer();
    const { parsedMessages, parseMessages } = useMessageParser(isStreaming);
    const lastConstructedMessage = useRef<any>(null);
    const sessionStore = SessionStore.getInstance();
    const previewErros = useRef<PreviewError[]>([]);
    const hasFetchedAllDataRef = useRef(false);
    const timeoutAllDataRef = useRef<NodeJS.Timeout>(null);

    interface PreviewError {
      type: 'error';
      message: string;
      source: string;
      lineno: string;
      colno: string;
      error: Error;
    }
    useEffect(() => {
      const previewErrorListener = (event: MessageEvent<PreviewError>) => {
        // Get the message data from the event object
        const message = event.data;

        // Process the message depending on its type
        if (message.type === 'error') {
          previewErros.current.push(event.data);

          workbenchStore.actionAlert.set({
            type: 'preview',
            title: 'Unhandled Promise Rejection',
            description: message.message,
            content: `Stack trace:\n${cleanStackTrace(message.error?.stack || message.message || message.source || '')}`,
            source: 'preview',
          });
        }
      };
      window.addEventListener('message', previewErrorListener);

      return () => {
        window.removeEventListener('message', previewErrorListener);
      };
    }, []);

    const customUseChatFetch = async (_: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      const sessionData = await sessionStore.getSessionData();

      const pingUrl = `${sessionData.host}/ping`;
      const streamUrl = `${sessionData.host}/ai/stream`;

      const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

      const waitForPing = async (pingUrl: string, maxRetries = 60, intervalMs = 1000) => {
        for (let i = 0; i < maxRetries; i++) {
          try {
            const res = await fetch(pingUrl, { method: 'GET', credentials: 'include' });

            if (res.ok) {
              return true;
            }
          } catch (err) {
            // ignore, might just be server not ready yet
          }
          await wait(intervalMs);
        }
        throw new Error(`Ping failed after ${maxRetries} attempts`);
      };

      await waitForPing(pingUrl);

      return await fetch(streamUrl, { ...init, credentials: 'include' });
    };

    useEffect(() => {
      if (initialMessages?.length) {
        setChatStarted(!!initialMessages?.length);
      }

      const shouldFetch =
        !hasFetchedAllDataRef.current &&
        ((chatStarted && estimationsProject && !isStreaming) || initialMessages?.length);

      if (!shouldFetch) {
        return;
      }

      if (timeoutAllDataRef.current) {
        clearTimeout(timeoutAllDataRef.current);
      }

      timeoutAllDataRef.current = setTimeout(async () => {
        try {
          const projectSlug = sessionStore.getSlug();
          const estResponse = await backendApiFetch(`/user-projects/${projectSlug}/all-data`);
          const data = await estResponse.json();
          const newTotalCost = data.totalTokensCost;

          if (lastTotalTokensCost !== null && newTotalCost > lastTotalTokensCost) {
            const diff = newTotalCost - lastTotalTokensCost;
            window.dataLayer = window.dataLayer || [];
            window.dataLayer.push({
              event: 'spend_virtual_currency',
              currency: 'tokens',
              value: diff,
              item_name: 'Prompt',
            });
          }

          setLastTotalTokensCost(newTotalCost);

          const formatedData = {
            projectName: data.projectName,
            totalTokens: data.totalTokens,
            timeMetrics: {
              traditional: data?.estimations?.estimatedTimeTraditional,
              actual: data.timeSpentAi,
            },
            estimatedCost: {
              traditional: data?.estimations?.estimatedCostTraditional,
              tokens: data.totalTokensCost,
            },
            estimations: {
              confidenceScore: data?.estimations?.confidenceScore,
              estimatedCostTraditional: data?.estimations?.estimatedCostTraditional,
              estimatedTimeTraditional: data?.estimations?.estimatedTimeTraditional,
              estimatedNumberOfDevelopers: data?.estimations?.estimatedNumberOfDevelopers,
              recommendedDeveloperLevel: data?.estimations?.recommendedDeveloperLevel,
              timeToMarket: data?.estimations?.timeToMarket,
              maintenanceCostPercentage: data?.estimations?.maintenanceCostPercentage,
              projectType: data?.estimations?.projectType,
              projectComplexity: data?.estimations?.projectComplexity,
              uniqueComponentCount: data?.estimations?.uniqueComponentCount,
              featureCount: data?.estimations?.featureCount,
              rangeOfUncertainty: data?.estimations?.rangeOfUncertainty,
              keyTechnologies: data?.estimations?.keyTechnologies,
              breakdown: data?.estimations?.breakdown,
            },
          };

          if (formatedData.estimatedCost.traditional) {
            setEstimationsData(formatedData);
          }

          hasFetchedAllDataRef.current = true;
        } catch (error) {
          console.error('Failed to fetch estimation data', error);
        }
      }, 3000);
    }, [initialMessages, isStreaming]);

    const { messages, isLoading, input, handleInputChange, setInput, stop, append } = useChat({
      fetch: customUseChatFetch,
      body: {
        // todo: delete files from here
        files: {
          iFiles: [],
          uFiles: [],
        },
        mode: chatStore.get().mode,
        contextOptimization: contextOptimizationEnabled,
        projectSlug: sessionStore.getSlug(),
        dynamicReasoning: localStorage.getItem('extendedThinkingMode') || 'first-response',
        model: localStorage.getItem(AI_MODEL_STORAGE_KEY) || AI_MODELS[0].value,
        differences: controlCenterStore.aiDiffMode.get(),
      },
      sendExtraMessageFields: true,
      onError: (error) => {
        logger.error('Request failed\n\n', error);
        setTryToRetry(true);
        let parsedError;

        if (error.message && typeof error.message === 'string' && error.message.includes('Not enough tokens')) {
          handleError(error);
          return;
        }

        try {
          parsedError = JSON.parse(error.message);
        } catch (e) {
          if (tokensLoaded && remainingTokens === 0) {
            setShowTokenCalculator(true);
          } else {
            if (window.showToast) {
              window.showToast({
                type: 'error',
                title: 'Error',
                message: 'Our AI is pretty busy right now, please try again in a minute',
                duration: 4000,
              });
            }
            //toast.error('Our AI is pretty busy right now, please try again in a minute');
          }
        }

        if (parsedError.message === `AI stream error for project ${workbenchStore.getSlug()}: Prompt is too long`) {
          setShowPromptTooLongDialog(true);
        } else if (
          parsedError.message &&
          typeof parsedError.message === 'string' &&
          parsedError.message.includes('Not enough tokens')
        ) {
          handleError(parsedError);
        } else if (
          error.message.includes('ERR_HTTP2_PROTOCOL_ERROR') ||
          error.message.includes('Failed to fetch') ||
          error.message.includes('NetworkError') ||
          error.message.includes('network')
        ) {
          if (window.showToast) {
            window.showToast({
              type: 'error',
              title: 'Error',
              message: 'Our AI is pretty busy right now, please try again in a minute',
              duration: 4000,
            });
          }
          //toast.error('Our AI is pretty busy right now, please try again in a minute');
        } else {
          if (window.showToast) {
            window.showToast({
              type: 'error',
              title: 'Error',
              message: `There was an error processing your request: ${error.message ? error.message : 'No details were returned'}`,
              duration: 4000,
            });
          }
          /*toast.error(
            'There was an error processing your request: ' +
              (error.message ? error.message : 'No details were returned'),
          );*/
        }
      },
      onResponse: () => {
        sessionStore.onStreamStart.set(new Date());
      },
      onFinish: (_, response) => {
        const projectSlug = sessionStore.getSlug();

        setTimeout(async () => {
          const estResponse = await backendApiFetch(`/user-projects/${projectSlug}/all-data`);
          const data = await estResponse.json();

          const newTotalCost = data.totalTokensCost;

          if (lastTotalTokensCost == null) {
            window.dataLayer = window.dataLayer || [];
            window.dataLayer.push({
              event: 'spend_virtual_currency',
              currency: 'tokens',
              value: newTotalCost,
              item_name: 'Prompt',
            });
          }

          setLastTotalTokensCost(newTotalCost);

          const formatedData = {
            projectName: data.projectName,
            totalTokens: data.totalTokens,
            timeMetrics: {
              traditional: data.estimations.estimatedTimeTraditional,
              actual: data.timeSpentAi,
            },
            estimatedCost: {
              traditional: data.estimations.estimatedCostTraditional,
              tokens: data.totalTokensCost,
            },
            estimations: {
              confidenceScore: data.estimations.confidenceScore,
              estimatedCostTraditional: data.estimations.estimatedCostTraditional,
              estimatedTimeTraditional: data.estimations.estimatedTimeTraditional,
              estimatedNumberOfDevelopers: data.estimations.estimatedNumberOfDevelopers,
              recommendedDeveloperLevel: data.estimations.recommendedDeveloperLevel,
              timeToMarket: data.estimations.timeToMarket,
              maintenanceCostPercentage: data.estimations.maintenanceCostPercentage,
              projectType: data.estimations.projectType,
              projectComplexity: data.estimations.projectComplexity,
              uniqueComponentCount: data.estimations.uniqueComponentCount,
              featureCount: data.estimations.featureCount,
              rangeOfUncertainty: data.estimations.rangeOfUncertainty,
              keyTechnologies: data.estimations.keyTechnologies,
              breakdown: data.estimations.breakdown,
            },
          };

          setEstimationsData(formatedData);
        }, 3000);

        sessionStore.onStreamEnd.set(new Date());

        refreshTokens();

        // @TO_BE_REMOVED after Webcontainer update
        setIsModifiedCode(true);

        const usage = response?.usage;

        if (usage) {
          logger.debug('Token usage:', usage);

          // Check if Extended Thinking is enabled to alert the user when the usage is high (100k prompt tokens)
          const extendedThinkingMode = localStorage.getItem('extendedThinkingMode');

          if (
            usage.promptTokens > EXTENDED_THINKING_TOKEN_THRESHOLD &&
            (extendedThinkingMode === 'always' || extendedThinkingMode === 'first-response')
          ) {
            if (window.showToast) {
              window.showToast({
                type: 'warning',
                title: 'Warning',
                message: `You are using Extended Thinking with a very large prompt. Frequent use of this feature with large prompts may significantly increase your usage and costs.`,
                duration: 4000,
              });
            }
            // toast.warning(
            //   'You are using Extended Thinking with a very large prompt. Frequent use of this feature with large prompts may significantly increase your usage and costs.',
            // );
          }
        }

        lastConstructedMessage.current = null;
        logger.debug('Finished streaming');
      },
      initialMessages,
      initialInput: Cookies.get(PROMPT_COOKIE_KEY) || '',
    });



    function setFirstUserMessageCookie(messages: Message[]) {
      if (!Array.isArray(messages) || messages.length === 0) {
        return;
      }

      const existingCookie = document.cookie.split('; ').find((row) => row.startsWith('first-user-message='));

      const cookieValue = existingCookie ? decodeURIComponent(existingCookie.split('=')[1]) : null;

      const firstUserMessage = messages.find((m) => m.role === 'user');

      if (!firstUserMessage || !firstUserMessage.content) {
        return;
      }

      if (cookieValue !== firstUserMessage.content) {
        document.cookie = `first-user-message=${encodeURIComponent(
          firstUserMessage.content
        )}; path=/; max-age=${60 * 60 * 24 * 365}`;
      }
    }

    useEffect(() => {
      if (messages && messages.length) {
        setFirstUserMessageCookie(messages);
      }
    }, [messages.length]);

    const handleClose = () => {
      setShowPromptTooLongDialog(false);

      lastConstructedMessage.current = null;
    };
    // Expose a global __appendMessage function for retrying messages
    useEffect(() => {
      window.__appendMessage = (msg) => {
        append(msg, {
          ...(msg.experimental_attachments?.length > 0 && {
            experimental_attachments: msg.experimental_attachments,
          }),
          body: {
            messages: [msg],
          },
        });
        setTryToRetry(false);
      };
      return () => {
        delete window.__appendMessage;
      };
    }, [tryToRetry]);

    const handleModelSwitch = (modelValue: string) => {
      if (lastConstructedMessage.current) {
        localStorage.setItem(AI_MODEL_STORAGE_KEY, modelValue);

        const msgToResend = { ...lastConstructedMessage.current };
        lastConstructedMessage.current = null;
        stop();
        setTimeout(() => {
          append({
            role: 'user',
            content:
              typeof msgToResend.content === 'string' ? msgToResend.content : JSON.stringify(msgToResend.content),
            id: Date.now().toString(),
            ...(msgToResend.experimental_attachments && {
              experimental_attachments: msgToResend.experimental_attachments,
            }),
          });
        }, 300);
      }
    };

    useEffect(() => {
      // Changed this from 'prompt' to 'chat-prompt' because google oauth returned a 'prompt' query and it would open up the chat, sending gibberish to the llm
      const prompt = searchParams.get('chat-prompt');

      if (!prompt) {
        return;
      }

      setSearchParams({});
      runAnimation();
      append({
        role: 'user',
        content: [
          {
            type: 'text',

            // [Model: ${model}]\n\n[Provider: ${provider.name}]\n\n
            text: `${prompt}`,
          },
        ] as any, // Type assertion to bypass compiler check
      });
    }, [searchParams]);

    useEffect(() => {
      chatStore.setKey('started', !!initialMessages.length);

      const storedApiKeys = Cookies.get('apiKeys');

      if (storedApiKeys) {
        setApiKeys(JSON.parse(storedApiKeys));
      }
    }, []);

    useEffect(() => {
      if (isStreaming !== isLoading) {
        setIsStreaming(isLoading);
      }

      if (!messages.length) {
        return;
      }

      processSampledMessages({
        messages,
        initialMessages,
        isLoading,
        parseMessages,
        storeMessageHistory,
      });
    }, [JSON.stringify(messages), isLoading]);

    // useEffect(() => {
    //   if (!actionAlert) {
    //     return () => void 0;
    //   }

    //   // Run and store timeouts for cleanup
    //   const logTimeout = ingestErrorLogs(actionAlert);
    //   const fixErrorTimeout = tryToFixError(actionAlert);

    //   return () => {
    //     clearTimeout(logTimeout);

    //     // Only clear fixErrorTimeout if it’s truthy
    //     if (fixErrorTimeout) {
    //       return;
    //     }

    //     clearTimeout(fixErrorTimeout);
    //   };
    // }, [actionAlert]);

    const scrollTextArea = () => {
      const textarea = textareaRef.current;

      if (textarea) {
        textarea.scrollTop = textarea.scrollHeight;
      }
    };

    const abort = () => {
      stop();
      chatStore.setKey('aborted', true);
      workbenchStore.abortAllActions();
    };

    const runAnimation = async () => {
      if (chatStarted) {
        return;
      }

      /*
       * await Promise.all([
       *   animate('#examples', { opacity: 0, display: 'none' }, { duration: 0.1 }),
       *   animate('#intro', { opacity: 0, flex: 1 }, { duration: 0.2, ease: cubicEasingFn }),
       * ]);
       */

      chatStore.setKey('started', true);

      setChatStarted(true);
    };

    const sendMessageWrapper = (originalSendMessage: typeof sendMessage) => {
      return (event: UIEvent, messageInput?: string, messageOptions?: JSONValue) => {
        if (messageInput === 'Create a database for my project') {
          append({
            role: 'user',
            content: 'Creating the connection with the database',
          } as Message);

          originalSendMessage(event, messageInput, messageOptions);
        } else {
          originalSendMessage(event, messageInput, messageOptions);
        }
        if (!messageInput?.startsWith('*Fix this')) {
          resetErrorTracking();
        }
      };
    };

    const handleBuyTokens = async (tokens: string): Promise<void> => {
      await createCheckoutSession({
        mode: 'payment',
        tokens,
      });
    };

    const [showTokenCalculator, setShowTokenCalculator] = useState<boolean>(false);
    const [dismissedNoTokensDialog, setDismissedNoTokensDialog] = useState(false);
    const [tokensLoaded, setTokensLoaded] = useState(false);

    const tokensStoreCallback = () => {
      setTokensLoaded(true);
    };

    const handleError = (error: any) => {
      if (error.message && error.message.includes('Not enough tokens')) {
        setShowTokenCalculator(true);
      }
    };

    const tokensStore = TokensStore.getInstance(tokensStoreCallback);
    const tokensData = useStore(tokensStore._tokensData);
    const remainingTokens = tokensData?.remainingTokens || 0;

    async function refreshTokens() {
      await tokensStore.refreshTokensData();
      tokensStoreCallback();
    }

    if (remainingTokens === null) {
      return null;
    }

    useEffect(() => {
      if (remainingTokens !== null && remainingTokens <= 0) {
        setDismissedNoTokensDialog(false);
      }
    }, [remainingTokens]);

    const sendMessage = async (_event: UIEvent, messageInput?: string, messageOptions?: JSONValue) => {
      let _input = messageInput || input;

      workbenchStore.userSelectedFileManually = false;

      if (_input.length === 0 || isLoading) {
        return;
      }

      if (contentPrompt.length > CONTENT_PROMPT.length) {
        _input =
          _input + ' bielaImagesPrompt ' + contentPrompt.replace('nextImagePlaceholder', '') + ' bielaImagesPrompt';
        // Reset content prompt
        setContentPrompt(CONTENT_PROMPT);
      }

      // if user preferences attach content to prompt remove the content studio images from the imageDataList
      const userPreferences = await getUserPreferences();

      const imagesToSend = !userPreferences?.attachContentToPrompt
        ? imageDataList.filter((_, index) => !uploadedFiles[index]?.name.startsWith('resource-'))
        : imageDataList;

      /**
       * @note (delm) Usually saving files shouldn't take long but it may take longer if there
       * many unsaved files. In that case we need to block user input and show an indicator
       * of some kind so the user is aware that something is happening. But I consider the
       * happy case to be no unsaved files and I would expect users to save their changes
       * before they send another message.
       */

      chatStore.setKey('aborted', false);

      runAnimation();

      if (!chatStarted && messageInput && autoSelectTemplate) {
        setFakeLoading(true);
        setMessages([
          {
            id: `${new Date().getTime()}`,
            role: 'user',
            content: [
              {
                type: 'text',

                // [Model: ${model}]\n\n[Provider: ${provider.name}]
                text: `${_input}`,
              },
              ...imagesToSend.map((imageData) => ({
                type: 'image',
                image: imageData,
              })),
            ] as any, // Type assertion to bypass compiler check
          },
        ]);

        // reload();

        const { template, title } = await selectStarterTemplate({
          message: messageInput,
        });

        if (template !== 'blank') {
          const temResp = await getTemplates(template, title).catch((e) => {
            if (e.message.includes('rate limit')) {
              if (window.showToast) {
                window.showToast({
                  type: 'warning',
                  title: 'Warning',
                  message: 'Rate limit exceeded. Skipping starter template\n Continuing with blank template',
                  duration: 4000,
                });
              }
              //toast.warning('Rate limit exceeded. Skipping starter template\n Continuing with blank template');
            } else {
              if (window.showToast) {
                window.showToast({
                  type: 'warning',
                  title: 'Warning',
                  message: 'Failed to import starter template\n Continuing with blank template',
                  duration: 4000,
                });
              }
              //toast.warning('Failed to import starter template\n Continuing with blank template');
            }

            return null;
          });

          if (temResp) {
            const { assistantMessage, userMessage } = temResp;

            setMessages([
              {
                id: `${new Date().getTime()}`,
                role: 'user',
                content: messageInput,

                // annotations: ['hidden'],
              },
              {
                id: `${new Date().getTime()}`,
                role: 'assistant',
                content: assistantMessage,
              },
              {
                id: `${new Date().getTime()}`,
                role: 'user',

                // [Model: ${model}]\n\n[Provider: ${provider.name}]\n\n
                content: `${userMessage}`,
                annotations: ['hidden'],
              },
            ]);

            reload();
            setFakeLoading(false);

            return;
          } else {
            setMessages([
              {
                id: `${new Date().getTime()}`,
                role: 'user',
                content: [
                  {
                    type: 'text',

                    // [Model: ${model}]\n\n[Provider: ${provider.name}]\n\n
                    text: `${_input}`,
                  },
                  ...imagesToSend.map((imageData) => ({
                    type: 'image',
                    image: imageData,
                  })),
                ] as any, // Type assertion to bypass compiler check
              },
            ]);
            reload();
            setFakeLoading(false);

            return;
          }
        } else {
          setMessages([
            {
              id: `${new Date().getTime()}`,
              role: 'user',
              content: [
                {
                  type: 'text',

                  // [Model: ${model}]\n\n[Provider: ${provider.name}]\n\n
                  text: `${_input}`,
                },
                ...imagesToSend.map((imageData) => ({
                  type: 'image',
                  image: imageData,
                })),
              ] as any, // Type assertion to bypass compiler check
            },
          ]);
          reload();
          setFakeLoading(false);

          return;
        }
      }
      const mode = chatStore.get().mode;

      const newMessage = {
        role: 'user',
        content: _input,
        ...(imagesToSend?.length > 0 && {
          experimental_attachments: imagesToSend.map((imageData, index) => {
            const file = uploadedFiles[index];
            const contentType = file?.type.startsWith('image/') ? 'image' : 'file';
            return {
              contentType,
              url: imageData,
              ...(contentType === 'file' && file?.name && { name: file.name }),
            };
          }),
        }),
        isChatMsg: mode === 'chat',
        data: messageOptions,
      };

      // Store this message for potential resending
      lastConstructedMessage.current = newMessage;

      append(newMessage as any, {
        ...(imagesToSend?.length > 0 && {
          experimental_attachments: imagesToSend.map((imageData, index) => {
            const file = uploadedFiles[index];
            const contentType = file?.type.startsWith('image/') ? 'image' : 'file';
            return {
              contentType,
              url: imageData,
              ...(contentType === 'file' && file?.name && { name: file.name }),
            };
          }),
        }),
        body: {
          messages: [newMessage],
        },
      });

      setInput('');
      Cookies.remove(PROMPT_COOKIE_KEY);

      // Add file cleanup here
      setUploadedFiles([]);
      setImageDataList([]);

      resetEnhancer();

      textareaRef.current?.blur();
    };

    /**
     * Handles the change event for the textarea and updates the input state.
     * @param event - The change event from the textarea.
     */
    const onTextareaChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
      handleInputChange(event);
    };

    /**
     * Debounced function to cache the prompt in cookies.
     * Caches the trimmed value of the textarea input after a delay to optimize performance.
     */
    const debouncedCachePrompt = useCallback(
      debounce((event: React.ChangeEvent<HTMLTextAreaElement>) => {
        const trimmedValue = event.target.value.trim();
        Cookies.set(PROMPT_COOKIE_KEY, trimmedValue, { expires: 30 });
      }, 1000),
      []
    );

    const [messageRef, scrollRef] = useSnapScroll();

    function extractTitle(content: string): string | null {
      const match = content.match(/data-message-id="([^"]+)"/);

      if (match && match[1]) {
        const messageId = match[1];
        const artifacts = workbenchStore.artifacts.get();
        const artifact = artifacts[messageId];

        if (artifact && artifact.title) {
          return artifact.title;
        }
      }

      return null;
    }

    useEffect(() => {
      const getCookie = (name: string) => {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);

        if (parts.length === 2) {
          return decodeURIComponent(parts.pop()!.split(';').shift()!);
        }

        return null;
      };

      const promptValue = getCookie('project_prompt');

      if (promptValue) {
        setChatStarted(true);
        setInput(promptValue);
        sendMessage({} as UIEvent, promptValue);
        document.cookie = 'project_prompt=; path=/; domain=biela.dev; expires=Thu, 01 Jan 1970 00:00:00 UTC;';
      }
    }, []);

    return (
      <Wrapper
        handleSendMessage={sendMessage}
        isStreaming={isLoading || loadingFromBackend}
        handleStop={abort}
        footerOff={chatStarted || loadingFromBackend}
        chatStarted={chatStarted || loadingFromBackend}
      >
        <div
          className={`chat-page bg-[#0a1730]  relative flex flex-col ${!chatStarted ? 'min-h-[900px]' : 'h-screen'} w-screen `}
        >
          <BaseChat
            loadingFromBackend={loadingFromBackend}
            ref={animationScope}
            textareaRef={textareaRef}
            input={input}
            setContentPrompt={setContentPrompt}
            showChat={showChat}
            chatStarted={chatStarted || loadingFromBackend}
            isStreaming={isLoading || fakeLoading || loadingFromBackend}
            enhancingPrompt={enhancingPrompt}
            promptEnhanced={promptEnhanced}
            sendMessage={sendMessageWrapper(sendMessage)}
            messageRef={messageRef}
            scrollRef={scrollRef}
            hasHistory={messages.length > 0}
            isTokenDialogOpen={
              tokensLoaded && ((remainingTokens === 0 && !dismissedNoTokensDialog) || showTokenCalculator)
            }
            onBuyTokens={handleBuyTokens}
            onCloseTokenDialog={() => {
              handleClose();
              setShowTokenCalculator(false);
              setDismissedNoTokensDialog(true);
            }}
            remainingTokens={remainingTokens}
            handleInputChange={(e) => {
              onTextareaChange(e);
              debouncedCachePrompt(e);
            }}
            handleStop={abort}
            description={description}
            importChat={importChat}
            exportChat={exportChat}
            messages={(messages as MessageWithHidden[])
              .filter((msg) => !msg.hiddenAt)
              .map((message, i) => {
                if (message.role === 'user') {
                  return message;
                }

                const title = extractTitle(parsedMessages[i] || '');

                return {
                  ...message,
                  content: parsedMessages[i] || '',
                  title,
                };
              })}
            enhancePrompt={() => {
              enhancePrompt(
                input,
                imageDataList,
                (input) => {
                  setInput(input);
                  scrollTextArea();
                },

                /*
                 * model, // use specific LLM model (string)
                 * provider, // use specific LLM provider (string)
                 */
                apiKeys
              );

              setUploadedFiles([]);
              setImageDataList([]);
            }}
            actionAlert={actionAlert}
            clearAlert={() => workbenchStore.clearAlert()}
          />

          <ErrorTooLongDialog
            isOpen={showPromptTooLongDialog}
            onClose={() => {
              setShowPromptTooLongDialog(false);
            }}
            onModelSwitch={handleModelSwitch}
          />
          <ErrorRunOutOfTokensDialog
            isOpen={tokensLoaded && ((remainingTokens === 0 && !dismissedNoTokensDialog) || showTokenCalculator)}
            onBuyTokens={handleBuyTokens}
            onClose={() => {
              handleClose();
              setShowTokenCalculator(false);
              setDismissedNoTokensDialog(true);
            }}
          />
        </div>
      </Wrapper>
    );
  }
);
