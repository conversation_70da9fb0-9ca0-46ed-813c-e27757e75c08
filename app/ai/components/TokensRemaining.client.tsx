import React, { useEffect, useMemo } from 'react';
import ClientOnly from '~/components/common/ClientOnly';
import { useNavigate } from 'react-router-dom';
import { TokensStore } from '~/ai/lib/stores/tokens/tokensStore';
import { FaRobot } from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import { useStore } from '@nanostores/react';

const TokenRemaining = ({ isHeader }: { isHeader?: boolean }) => {
  const navigate = useNavigate();
  const { t } = useTranslation('translation');
  const tokensStore = TokensStore.getInstance(() => {});
  const tokensData = useStore(tokensStore._tokensData);
  const remainingTokens = tokensData?.remainingTokens ?? null;
  const freePromptsRemaining = tokensData?.freeTokensRemaining ?? 0;

  useEffect(() => {
    tokensStore.refreshTokensData();
  }, []);

  const displayTokens = useMemo(
    () => (remainingTokens === null || remainingTokens === undefined || remainingTokens <= 0 ? 0 : remainingTokens),
    [remainingTokens]
  );

  const shouldShowComponent = useMemo(
    () => remainingTokens !== null || freePromptsRemaining === null,
    [remainingTokens, freePromptsRemaining]
  );

  if (!shouldShowComponent) {
    return null;
  }

  return (
    <ClientOnly>
      {() => (
        <div className="">
          {isHeader ? (
            <div className="hidden min-[840px]:flex items-center gap-2 bg-[#11182780] px-4 py-2 rounded-full">
              <FaRobot className="text-green-400 text-[16px]" />
              <span className={'text-white font-manrope font-extralight flex gap-1'}>
                {new Intl.NumberFormat('en', { notation: 'compact', compactDisplay: 'short' }).format(displayTokens)}

                <span className={'sm:block hidden'}>{t('tokensAvailable', 'tokens available')}</span>
              </span>
            </div>
          ) : freePromptsRemaining > 0 ? (
            <div style={{ color: '#FFD700' }}>
              {freePromptsRemaining} free prompt{freePromptsRemaining > 1 ? 's' : ''} remaining
            </div>
          ) : displayTokens > 0 ? (
            <>
              <div style={{ display: 'flex', gap: '8px' }}>
                <img src="/icons/coin.svg" alt="Coins" />
                Tokens remaining
              </div>
              <span style={{ color: '#4ADE80' }}>{new Intl.NumberFormat('de-DE').format(displayTokens)}</span>
            </>
          ) : (
            <div
              style={{ color: '#FF6347', cursor: 'pointer', textDecoration: 'underline' }}
              onClick={() => navigate('/?register=true')}
            >
              Sign Up & Get More Tokens
            </div>
          )}
        </div>
      )}
    </ClientOnly>
  );
};

export default TokenRemaining;
