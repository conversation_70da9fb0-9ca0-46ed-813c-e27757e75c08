// BaseChat.tsx
/*
 * @ts-nocheck
 * Preventing TS checks with files presented in the video for a better presentation.
 */
import type { JSONValue, Message } from 'ai';
import React, { lazy, type RefCallback, Suspense, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import ClientOnly from '~/components/common/ClientOnly';
import { classNames } from '~/utils/classNames';
import { Messages } from './Messages.client';
import { SendButton } from './SendButton.client';
import * as Tooltip from '@radix-ui/react-tooltip';
import styles from '../styles/BaseChat.module.scss';
import FilePreview from './FilePreview';
import { ScreenshotStateManager } from './ScreenshotStateManager';
import type { ActionAlert } from '~/types/actions';
import '../../components/styles/ainmated-spinner.scss';
import { ChatCodeSwitch } from '~/ai/components/ChatCodeSwitch';
import WithTooltip from './Tooltip';

import { createScopedLogger } from '~/utils/logger';
import ChatSuggestion from '~/ai/components/ChatSuggestion';
import CheckList from '~/assets/icons/checkList';
import {
  chatStore,
  setChatMode,
  setCheckingList,
  setCleaningProject,
  setImageDataList,
  setShowPrompt,
  setTryToRetry,
  setUploadedFiles,
} from '~/ai/lib/stores/chat';
import { useStore } from '@nanostores/react';
import { useNavigate, useParams } from 'react-router-dom';
import { backendApiFetch } from '~/ai/lib/backend-api';
import RoadmapContent from '~/ai/components/RoadmapContent';
import { AnimatePresence, motion } from 'framer-motion';
// Import the new TabsSection component (adjust the path as needed)
import TabsSection from '~/components/common/TabsSection';
import ActionButtons from '~/components/common/ActionButtons';
import AIModelSelector from '~/components/common/AIModelSelector';
import { RoadmapSwitch } from '~/ai/components/RoadmapSwitch';
import HistoryContent from '~/ai/components/HistoryContent';
import { ChevronDownIcon } from 'lucide-react';
import { useUser } from '~/ai/lib/context/userContext';
import { useTranslation } from 'react-i18next';
import { useChatOpen } from '../lib/context/chatOpenContext';
import ProjectIdeasCarousel from './ProjectIdeasCarousel';
import CardSuccessModal from '~/backOffice/components/settings/IdentitySettings/CardSuccessModal';
import { StatusPopup } from '~/routes/profile';
import JoinUsLiveYoutube from '~/components/banners/JoinUsLiveYoutube';
import { TokensStore } from '~/ai/lib/stores/tokens/tokensStore';
import { workbenchStore } from '../lib/stores/workbench';
import { AI_MODEL_STORAGE_KEY, AI_MODELS } from '~/utils/constants';
import { sessionStore } from '~/ai/lib/stores/session/sessionStore';

import UpgradePlanPromotion from '~/components/banners/UpgradePlanPromotion';
import { fetchCurrentUserPlan } from '~/lib/stores/billing';
import { Plan } from '~/types/billing';
import PostDisconnectProjectModal from '~/components/supabase/PostDisconnectProjectModal';
import PostLinkProjectModal from '~/components/supabase/PostLinkProjectModal';
import SupabaseActionInProgressModal from '~/components/supabase/SupabaseActionInProgressModal';
import ErrorHandler from '~/ai/components/ErrorHandlerManual';
import { ActionAlertCollect } from '~/ai/components/ActionAlertCollect';
import { FaTrophy } from 'react-icons/fa';
import { controlCenterStore } from '~/ai/lib/stores/controlCenter';

const Workbench = lazy(() => import('~/ai/components/workbench/Workbench.client'));
const logger = createScopedLogger('BaseChat');

type MessageWithHidden = Message & { hiddenAt?: string };

interface BaseChatProps {
  loadingFromBackend?: boolean;
  textareaRef?: React.RefObject<HTMLTextAreaElement | undefined>;
  messageRef?: RefCallback<HTMLDivElement> | undefined;
  scrollRef?: RefCallback<HTMLDivElement> | undefined;
  showChat?: boolean;
  chatStarted?: boolean;
  isStreaming?: boolean;
  messages?: Message[];
  description?: string;
  enhancingPrompt?: boolean;
  promptEnhanced?: boolean;
  input?: string;
  setContentPrompt?: React.Dispatch<React.SetStateAction<string>>;
  handleStop?: () => void;
  sendMessage?: (event: React.UIEvent, messageInput?: string, messageOptions?: JSONValue) => void;
  handleInputChange?: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
  enhancePrompt?: () => void;
  importChat?: (description: string, messages: Message[]) => Promise<void>;
  exportChat?: () => void;
  actionAlert?: ActionAlert;
  clearAlert?: () => void;
  isTokenDialogOpen?: boolean;
  onBuyTokens?: (tokens: string) => Promise<void>;
  onCloseTokenDialog?: () => void;
  hasHistory?: boolean;
}

export const BaseChat = React.forwardRef<HTMLDivElement, BaseChatProps>(
  (
    {
      loadingFromBackend = false,
      textareaRef,
      messageRef,
      scrollRef,
      showChat = true,
      chatStarted = false,
      isStreaming = false,
      input = '',
      setContentPrompt,
      enhancingPrompt,
      handleInputChange,
      enhancePrompt,
      sendMessage,
      handleStop,
      messages,
      actionAlert,
      clearAlert,
      isTokenDialogOpen,
      onBuyTokens,
      onCloseTokenDialog,
      hasHistory = false,
    },
    ref
  ) => {
    const { t } = useTranslation('translation');
    const prevStreamingRef = useRef(isStreaming);
    const uploadedFiles = useStore(chatStore).uploadedFiles;
    const imageDataList = useStore(chatStore).imageDataList;
    const automaticError = useStore(controlCenterStore.automaticError);
    const [showPostDisconnectModal, setShowPostDisconnectModal] = useState(false);
    const [isListening, setIsListening] = useState(false);
    const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
    const [transcript, setTranscript] = useState('');
    const { showPrompt, checkingList } = useStore(chatStore);
    const { mode } = useStore(chatStore);
    const { chatId } = useParams();
    const [rotation, setRotation] = useState(0);
    const [isStatusPopupOpen, setIsStatusPopupOpen] = useState(false);
    const navigate = useNavigate();
    const dynamicStyleRef = useRef<HTMLStyleElement | null>(null);
    const [uploadedContentIndex, setUploadedContentIndex] = useState(0);
    const [plan, setPlan] = useState<Plan | null>(null);
    const [modelColor, setModelColor] = useState(() => {
      try {
        const currentModelId = localStorage.getItem(AI_MODEL_STORAGE_KEY) || AI_MODELS[0].value;
        const currentModel = AI_MODELS.find((model) => model.value === currentModelId) || AI_MODELS[0];

        return currentModel.color;
      } catch (error) {
        return '#4ADE80';
      }
    });
    const [isModelSelectorOpen, setIsModelSelectorOpen] = useState(false);

    useEffect(() => {
      // only in Code mode do we care about streaming transitions
      if (mode === 'code') {
        if (!prevStreamingRef.current && isStreaming) {
          // stream just started → show Code
          workbenchStore.currentView.set('code');

          window.dispatchEvent(new CustomEvent('files-updating'));
        }
      }

      // update for next render
      prevStreamingRef.current = isStreaming;
    }, [isStreaming, mode]);

    useEffect(() => {
      const loadPlan = async () => {
        try {
          const userPlan = await fetchCurrentUserPlan();
          setPlan(userPlan);
        } catch {}
      };
      if (isLoggedIn()) {
        loadPlan();
      }
    }, []);

    const updateDynamicStyles = (color: string) => {
      if (!dynamicStyleRef.current) {
        dynamicStyleRef.current = document.createElement('style');
        dynamicStyleRef.current.id = 'base-chat-dynamic-styles';
        document.head.appendChild(dynamicStyleRef.current);
      }

      let r, g, b;

      if (color.startsWith('#')) {
        const hex = color.slice(1);
        r = parseInt(hex.substring(0, 2), 16);
        g = parseInt(hex.substring(2, 4), 16);
        b = parseInt(hex.substring(4, 6), 16);
      } else {
        r = 134;
        g = 244;
        b = 156;
      }

      const css = `
        .base-chat:not(.chat-mode) .container-textarea:after {
          background: linear-gradient(var(--rotaion-variable), rgba(34,32,38,1) 70%, rgba(${r},${g},${b},1) 80%, rgba(${r},${g},${b},1) 100%) !important;
        }
      `;

      dynamicStyleRef.current.textContent = css;
    };

    useEffect(() => {
      updateDynamicStyles(modelColor);

      return () => {
        if (dynamicStyleRef.current) {
          document.head.removeChild(dynamicStyleRef.current);
        }
      };
    }, []);

    useEffect(() => {
      const handleModelChangeEvent = (event: CustomEvent<{ modelValue: string; source?: string }>) => {
        if (event.detail && event.detail.modelValue) {
          const newModel = AI_MODELS.find((model) => model.value === event.detail.modelValue);

          if (newModel) {
            setModelColor(newModel.color);
            updateDynamicStyles(newModel.color);
          }
        }
      };

      window.addEventListener('biela:changeAIModel', handleModelChangeEvent as EventListener);

      return () => {
        window.removeEventListener('biela:changeAIModel', handleModelChangeEvent as EventListener);
      };
    }, []);

    useEffect(() => {
      if (actionAlert?.type === 'SERVICE_UNAVAILABLE') {
        setIsStatusPopupOpen(true);
      } else {
        setIsStatusPopupOpen(false);
      }
    }, [actionAlert, isStreaming]);

    useEffect(() => {
      setInputValue(input);
    }, [input]);
    const { setIsChatOpen } = useChatOpen();
    // Add state for the active tab (defaulting to 'chat')
    const [activeTab, setActiveTab] = useState('chat');
    const [inputValue, setInputValue] = useState('');
    const [isDragActive, setIsDragActive] = useState(false);
    const { getUser, isLoggedIn } = useUser();
    const user = getUser();

    useEffect(() => {
      if (isStreaming && mode === 'chat') {
        setActiveTab('chat');
      }
    }, [isStreaming, mode]);

    const selectedView = useStore(workbenchStore.currentView);

    const handleIdeaClick = (idea: string) => {
      setInputValue(idea);
      setHasContent(idea.trim().length > 0);
      handleInputChange?.({ target: { value: idea } } as React.ChangeEvent<HTMLTextAreaElement>);
    };

    const [showPostLinkModal, setShowPostLinkModal] = useState(false);
    const [pendingLinkData, setPendingLinkData] = useState<{
      projectSlug: string;
      selectedProjectId: string;
      databaseName: string;
      onSuccess?: (
        selected: 'analyze' | 'display',
        fetchDatabaseSchemaAndSendToAI: typeof fetchDatabaseSchemaAndSendToAI
      ) => void;
      fetchDatabaseSchemaAndSendToAI?: (
        projectSlug: string,
        operationType: 'link' | 'duplicate' | 'normal',
        displayOnly?: boolean
      ) => Promise<void>;
    } | null>(null);

    const openPostLinkModal = (
      projectSlug: string,
      selectedProjectId: string,
      databaseName: string,
      onSuccess: (
        selected: 'analyze' | 'display',
        fetchDatabaseSchemaAndSendToAI: typeof fetchDatabaseSchemaAndSendToAI
      ) => void,
      fetchDatabaseSchemaAndSendToAI: (
        projectSlug: string,
        operationType: 'link' | 'duplicate' | 'normal',
        displayOnly?: boolean
      ) => Promise<void>
    ) => {
      setPendingLinkData({ projectSlug, selectedProjectId, databaseName, onSuccess, fetchDatabaseSchemaAndSendToAI });
      setShowPostLinkModal(true);
      setDisableSendButton?.(true);
    };

    const [hasContent, setHasContent] = useState(false);

    useEffect(() => {
      setHasContent(inputValue.trim().length > 0 || input.trim().length > 0);
    }, [inputValue, input]);

    const fetchUserData = async (userId: any) => {
      try {
        const response = await backendApiFetch(`/user/${userId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
        if (response.ok) {
          return await response.json();
        }
        return null;
      } catch (error) {
        console.error('Error fetching user data:', error);
        return null;
      }
    };

    useEffect(() => {
      let active = true;

      // Pornim animația de la starea curentă a rotației
      let angle = rotation;

      function updateRotation() {
        if (!active) {
          return;
        }

        // Incrementăm unghiul și îl normalizăm la [0, 360)
        angle = (angle + 1) % 360;
        setRotation(angle);

        /*
         * Dacă streaming-ul este activ, continuăm animația.
         * Dacă nu, mai continuăm doar până când unghiul revine la 0.
         */
        if (isStreaming || angle !== 0) {
          setTimeout(() => {
            requestAnimationFrame(() => {
              if (active) {
                updateRotation();
              }
            });
          }, 5000 / 360);
        } else {
          // Când animația s-a terminat (angle a revenit la 0), oprim animația.
          setRotation(0);
        }
      }

      /*
       * Pornim animația dacă:
       * - streaming-ul e activ, sau
       * - streaming-ul a devenit false, dar animația nu s-a terminat încă (rotation !== 0)
       */
      if (isStreaming || rotation !== 0) {
        updateRotation();
      }

      // Cleanup: oprim orice actualizare ulterioară când componenta se demontează sau când 'isStreaming' se schimbă

      return () => {
        active = false;
      };
    }, [isStreaming]);

    useEffect(() => {
      if (chatId) {
        localStorage.setItem('chatId', chatId);
      } else {
        setIsChatOpen(false);
      }
    }, [chatId]);

    const abort = async () => {
      if (isStreaming) {
        try {
          await sessionStore.abortStream();

          handleStop?.();
        } catch (error) {
          logger.error('Error during abort:', error);
          handleStop?.();
        }
      }
    };

    /**
     * Keep an eye on transcript changes for debugging
     */
    useEffect(() => {
      logger.debug('Transcript from speech:', transcript);
    }, [transcript]);

    useEffect(() => {
      localStorage.removeItem('projectUrl');
      localStorage.removeItem('annonKey');

      // Setup speech recognition
      if (typeof window !== 'undefined' && ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window)) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        const recognition = new SpeechRecognition();
        recognition.continuous = true;
        recognition.interimResults = true;

        recognition.onresult = (event) => {
          const transcript = Array.from(event.results)
            .map((result) => result[0])
            .map((result) => result.transcript)
            .join('');

          setTranscript(transcript);
          setInputValue(transcript);

          if (handleInputChange) {
            const syntheticEvent = {
              target: { value: transcript },
            } as React.ChangeEvent<HTMLTextAreaElement>;
            handleInputChange(syntheticEvent);
          }
        };

        recognition.onerror = (event) => {
          console.error('Speech recognition error:', event.error);
          setIsListening(false);
        };

        setRecognition(recognition);
      }

      // Clear the input
      if (handleInputChange) {
        const syntheticEvent = {
          target: { value: '' },
        } as React.ChangeEvent<HTMLTextAreaElement>;
        handleInputChange(syntheticEvent);
      }
    }, []);

    const startListening = (language: string) => {
      if (recognition) {
        recognition.lang = language;
        recognition.start();
        setIsListening(true);
      }
    };

    const stopListening = () => {
      if (recognition) {
        recognition.stop();
        setIsListening(false);
      }
    };

    const handleSendMessage = (event: React.UIEvent, messageInput?: string) => {
      const user = getUser();
      if (user) {
        if (sendMessage) {
          sessionStorage.setItem('newChat', (!chatStarted).toString());
          setIsChatOpen(true);
          sendMessage(event, messageInput);
          setInputValue('');
          // If speech recognition is active, abort it
          if (recognition) {
            recognition.abort();
            setTranscript('');
            setIsListening(false);

            // Clear the input
            if (handleInputChange) {
              const syntheticEvent = {
                target: { value: '' },
              } as React.ChangeEvent<HTMLTextAreaElement>;
              handleInputChange(syntheticEvent);
            }
          }
        }
      } else {
        document.cookie = `project_prompt=${encodeURIComponent(messageInput || '')}; path=/; domain=biela.dev; max-age=7200; Secure; SameSite=Lax`;
        navigate('/?login=true');
      }
    };

    const pushFile = (file: File, dataUrl: string) => {
      setUploadedFiles([...uploadedFiles, file]);
      setImageDataList([...imageDataList, dataUrl]);
    };

    const fileToDataURL = (file: File) =>
      new Promise<string>((res, rej) => {
        const r = new FileReader();
        r.onload = () => res(r.result as string);
        r.onerror = rej;
        r.readAsDataURL(file);
      });

    const processAndAddFile = async (file: File) => {
      const MAX_FILE_SIZE = 1 * 1024 * 1024; // 1 MB
      let finalFile = file;

      if (file.type.startsWith('image/') && file.size > MAX_FILE_SIZE) {
        try {
          finalFile = await optimizeImage(file);
        } catch (err) {
          console.error('Image optimisation failed:', err);
        }
      }

      const dataUrl = await fileToDataURL(finalFile);
      pushFile(finalFile, dataUrl);
    };

    const handleFileUpload = () => {
      const picker = document.createElement('input');
      picker.type = 'file';
      picker.accept =
        '.pdf,.doc,.docx,.xls,.xlsx,.pptx,.txt,.html,.css,.js,.json,.csv,.rtf,.tsx,.jsx,.env,.jpg,.jpeg,.png,.gif,.bmp,.webp,.svg,.tiff,.tif,.ico,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.openxmlformats-officedocument.presentationml.presentation,text/plain,text/html,text/css,text/javascript,application/javascript,application/json,text/csv,application/rtf,text/typescript,application/typescript,text/jsx,image/*';
      picker.multiple = true;

      picker.onchange = async (e) => {
        const files = Array.from((e.target as HTMLInputElement).files || []);

        const allowedExtensions = [
          '.pdf',
          '.doc',
          '.docx',
          '.xls',
          '.xlsx',
          '.pptx',
          '.txt',
          '.html',
          '.css',
          '.js',
          '.json',
          '.csv',
          '.rtf',
          '.tsx',
          '.jsx',
          '.env',
          '.jpg',
          '.jpeg',
          '.png',
          '.gif',
          '.bmp',
          '.webp',
          '.svg',
          '.tiff',
          '.tif',
          '.ico',
        ];
        const allowedMimeTypes = [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          'text/plain',
          'text/html',
          'text/css',
          'text/javascript',
          'application/javascript',
          'application/json',
          'text/csv',
          'application/rtf',
          'text/typescript',
          'application/typescript',
          'text/jsx',
          'image/jpeg',
          'image/png',
          'image/gif',
          'image/bmp',
          'image/webp',
          'image/svg+xml',
          'image/tiff',
          'image/x-icon',
        ];

        const validFiles = files.filter((file) => {
          const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
          const isValid =
            allowedMimeTypes.includes(file.type) ||
            allowedExtensions.includes(fileExtension) ||
            file.type.startsWith('image/');

          if (!isValid) {
            if (window.showToast) {
              window.showToast({
                type: 'error',
                title: 'Invalid File',
                message: `File ${file.name} is not supported.`,
                duration: 4000,
              });
            }
          }
          return isValid;
        });

        const newFiles: File[] = [];
        const newDataUrls: string[] = [];

        for (const file of validFiles) {
          let finalFile = file;

          if (file.type.startsWith('image/') && file.size > 1 * 1024 * 1024) {
            try {
              finalFile = await optimizeImage(file);
            } catch (err) {
              console.error('Image optimisation failed:', err);
              finalFile = file;
            }
          }
          const dataUrl = await fileToDataURL(finalFile);

          newFiles.push(finalFile);
          newDataUrls.push(dataUrl);
        }

        const currentFiles = chatStore.get().uploadedFiles || [];
        const currentDataUrls = chatStore.get().imageDataList || [];

        setUploadedFiles([...currentFiles, ...newFiles]);
        setImageDataList([...currentDataUrls, ...newDataUrls]);
      };

      picker.click();
    };

    const optimizeImage = (file: File): Promise<File> => {
      const MAX_FILE_SIZE = 1 * 1024 * 1024; // 1MB

      return new Promise((resolve, reject) => {
        const img = new Image();
        const reader = new FileReader();

        reader.onload = (e) => {
          img.src = e.target?.result as string;
        };

        reader.onerror = reject;
        reader.readAsDataURL(file);

        img.onload = () => {
          const MAX_WIDTH = 1024;
          const MAX_HEIGHT = 1024;

          let width = img.width;
          let height = img.height;

          if (width > height) {
            if (width > MAX_WIDTH) {
              height = (height * MAX_WIDTH) / width;
              width = MAX_WIDTH;
            }
          } else {
            if (height > MAX_HEIGHT) {
              width = (width * MAX_HEIGHT) / height;
              height = MAX_HEIGHT;
            }
          }

          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          if (ctx) {
            canvas.width = width;
            canvas.height = height;
            ctx.drawImage(img, 0, 0, width, height);

            const tryOptimize = (quality: number) => {
              canvas.toBlob(
                (blob) => {
                  if (blob) {
                    const optimizedFile = new File([blob], file.name, {
                      type: file.type,
                    });
                    if (optimizedFile.size <= MAX_FILE_SIZE) {
                      resolve(optimizedFile);
                    } else {
                      if (quality > 0.1) {
                        tryOptimize(quality - 0.05);
                      } else {
                        resolve(optimizedFile);
                      }
                    }
                  } else {
                    reject(new Error('Blob creation failed'));
                  }
                },
                'image/jpeg',
                quality
              );
            };

            tryOptimize(0.7);
          }
        };
      });
    };

    const handlePaste = async (e: React.ClipboardEvent) => {
      const items = e.clipboardData?.items;

      if (!items) {
        return;
      }

      for (const item of items) {
        if (item.type.startsWith('image/')) {
          e.preventDefault();

          const file = item.getAsFile();

          if (file) {
            const reader = new FileReader();

            reader.onload = async (e) => {
              const base64Image = e.target?.result as string;

              // Validate & re-encode image
              const img = new Image();

              img.onload = () => {
                let width = img.width;
                let height = img.height;
                const aspectRatio = img.width / img.height;
                const isVerticalImage = aspectRatio < 0.5 && img.height > 4000;

                const MAX_WIDTH = 1920;
                const MAX_HEIGHT = isVerticalImage ? 7500 : 1080;

                if (width > MAX_WIDTH || height > MAX_HEIGHT) {
                  if (isVerticalImage) {
                    const ratio = MAX_HEIGHT / height;
                    height = MAX_HEIGHT;
                    width = width * ratio;
                  } else {
                    const ratio = Math.min(MAX_WIDTH / width, MAX_HEIGHT / height);
                    width = width * ratio;
                    height = height * ratio;
                  }
                }

                const canvas = document.createElement('canvas');
                canvas.width = width;
                canvas.height = height;

                const ctx = canvas.getContext('2d');

                if (ctx) {
                  ctx.imageSmoothingEnabled = true;
                  ctx.imageSmoothingQuality = 'high';

                  ctx.drawImage(img, 0, 0, width, height);

                  let quality = 1;
                  const originalSize = file.size;

                  if (originalSize > 8000000) {
                    quality = 0.8;
                  } else if (originalSize > 4000000) {
                    quality = 0.9;
                  }

                  const processedImage = canvas.toDataURL('image/jpeg', quality);
                  pushFile(file, processedImage);
                }
              };

              img.onerror = (error) => {
                console.error('Error processing image:', error);
              };
              img.src = base64Image;
            };
            reader.readAsDataURL(file);
          }

          break;
        }
      }
    };

    const handleCleanUp = () => {
      if (isStreaming) {
        return;
      }

      setCleaningProject(true);

      const cleanUpPrompt =
        'Clean up the project by ensuring no single file exceeds 300 lines of code. Refactor large files into smaller, modular components while maintaining full functionality. Identify and remove all unused files, code, components, and any redundant data that are no longer needed. Ensure that all components remain properly connected and functional, avoiding any disruptions to the existing system. Maintain code integrity by verifying that no changes introduce errors or break current features. The goal is to optimize the project for efficiency, maintainability, and clarity.';
      sendMessage?.({} as React.UIEvent, cleanUpPrompt, {
        annotations: ['hidden'],
      });
    };

    const handleChecklist = () => {
      if (isStreaming) {
        return;
      }

      setCheckingList(true);

      const checklistPrompt =
        'Look at my initial prompt, understand the goal point by point and build for me a checklist with a green check for everything what has been done and with a red check what has left to be done.';
      sendMessage?.({} as React.UIEvent, checklistPrompt, {
        annotations: ['hidden'],
      });
    };

    const placeholderText = mode === 'code' ? t('codePlaceholder') : t('defaultPlaceholder');

    // Convert image URL to File
    const urlToFile = async (url: string, filename: string, mimeType: string): Promise<File> => {
      const res = await fetch(url);
      const blob = await res.blob();
      return new File([blob], filename, { type: mimeType });
    };

    // Simulate drop event with a single File
    const simulateResourceDrop = async (
      textarea: HTMLTextAreaElement | null | undefined,
      file: File,
      resource: { url: string; title: string; id: string; type: string; thumbnailUrl: string }
    ) => {
      const isVideo = resource.type.startsWith('video/');

      const dataTransfer = new DataTransfer();
      dataTransfer.items.add(file);

      const dropEvent = new DragEvent('drop', {
        bubbles: true,
        cancelable: true,
        dataTransfer,
      });

      textarea?.dispatchEvent(dropEvent);

      if (!isVideo) {
        return `${resource.title} image's url is: ${resource.url} \n`;
      } else {
        return `${resource.title} video's url is: ${resource.url} with thumbnail url: ${resource.thumbnailUrl} \n`;
      }
    };

    // Loop through and simulate all resources drops
    const simulateMultipleResourcesDrops = async (
      resources: { url: string; title: string; id: string; type: string; thumbnailUrl: string }[]
    ) => {
      let index = uploadedContentIndex;
      for (const resource of resources) {
        // if the resource is a video, use the thumbnail url for the preview
        const isVideo = resource.type.startsWith('video/');
        const fileUrl = isVideo ? resource.thumbnailUrl : resource.url;
        const fileType = isVideo ? 'image/jpeg' : resource.type;
        const fileId = index + '-' + resource.url.split('/')[resource.url.split('/').length - 1];
        index = index + 1;

        const file = await urlToFile(fileUrl, `resource-${fileId}`, fileType);
        const contentText = await simulateResourceDrop(textareaRef?.current, file, resource);
        if (setContentPrompt) {
          setContentPrompt((prev) => prev.replace('nextImagePlaceholder', contentText + 'nextImagePlaceholder'));
        }
      }
      setUploadedContentIndex(index);
    };

    const removeContentURLFromPrompt = (file: File): void => {
      // Content Studio uploaded file name example : resource-0-1747814608302.mp4".
      // Take the id out, take the prompt (inputValue), filter it to remove the lines including the file id and join them back together.
      if (file.name.startsWith('resource-')) {
        const fileId = file.name.split('-')[file.name.split('-').length - 1].split('.')[0];
        if (setContentPrompt) {
          setContentPrompt((prev) =>
            prev
              .split('\n')
              .filter((line) => !line.includes(fileId))
              .join('\n')
          );
        }
      }
    };

    const [isLoadingCardData, setIsLoadingCardData] = useState<boolean>(true);
    const [isLoadingCardDataError, setIsLoadingCardDataError] = useState<boolean>(false);
    const [cardVerified, setCardVerified] = useState<boolean>(false);
    const [cardDataLoaded, setCardDataLoaded] = useState(false);

    useEffect(() => {
      setIsLoadingCardData(true);
      setIsLoadingCardDataError(false);

      const checkCardVerified = async () => {
        try {
          const user = getUser();

          if (user?.id) {
            const res: { isCardVerified: boolean } = await fetchUserData(user.id);
            setCardVerified(res.isCardVerified);
          }
        } catch (err) {
          console.error('Failed to fetch card data:', err);
          setIsLoadingCardDataError(true);
        } finally {
          setIsLoadingCardData(false);
          setCardDataLoaded(true);
        }
      };

      void checkCardVerified();
    }, []);

    const [showCard, setShowCard] = useState(true);

    const historyScrollRef = useRef<HTMLDivElement>(null);

    const [showVerificationModal, setShowVerificationModal] = useState(false);
    useEffect(() => {
      if (window?.location?.search === '?verified') {
        setShowVerificationModal(true);
      }
    }, []);

    useEffect(() => {
      if (showVerificationModal) {
        const newUrl = window.location.href.split('?')[0];
        window.history.replaceState({}, document.title, newUrl);
      }
    }, [showVerificationModal]);

    const tokensStore = TokensStore.getInstance(() => {});
    const tokensData = useStore(tokensStore._tokensData);
    const remainingTokens = tokensData?.remainingTokens ?? null;
    const freePromptsRemaining = tokensData?.freeTokensRemaining ?? 0;

    const hasTokens = useMemo(
      () => !!remainingTokens || !!freePromptsRemaining,
      [remainingTokens, freePromptsRemaining]
    );

    useEffect(() => {
      const hideSpecificMessage = () => {
        const paragraphs = document.querySelectorAll('p');
        paragraphs.forEach((p) => {
          if (p.textContent === 'Create a database for my project') {
            const parentDiv = p.closest('.special-bg.user-message') as HTMLElement;

            if (parentDiv) {
              parentDiv.style.display = 'none';
            }
          }
        });
      };
      hideSpecificMessage();

      const observer = new MutationObserver(() => {
        hideSpecificMessage();
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });

      return () => {
        observer.disconnect();
      };
    }, []);

    const [popupStyle, setPopupStyle] = useState<{ width?: number; left?: number; top?: number }>({});
    const popupRef = useRef<HTMLDivElement>(null);
    const [popupHeight, setPopupHeight] = useState(0);
    const chatRef = useRef<HTMLDivElement>(null);
    const [showSupabaseActionModal, setShowSupabaseActionModal] = useState(false);
    const [supabaseActionType, setSupabaseActionType] = useState<'create' | 'link' | 'duplicate' | null>(null);
    const [disableSendButton, setDisableSendButton] = useState(false);
    const [shouldDisableOnFirstStream, setShouldDisableOnFirstStream] = useState(false);
    const [alreadyOpened, setAlreadyOpened] = useState(false);
    const updatePopupPosition = () => {
      if (!chatRef.current || !popupRef.current) return;
      const rect = chatRef.current.getBoundingClientRect();
      setPopupStyle({ width: rect.width, left: 0, top: 0 });
      setPopupHeight(popupRef.current.offsetHeight);
    };

    useLayoutEffect(() => {
      if (showPostDisconnectModal || showSupabaseActionModal || showPostLinkModal) {
        // immediate measurement
        updatePopupPosition();

        // next‐frame measurement, for portalled children
        const rafId = requestAnimationFrame(updatePopupPosition);

        // keep it up‐to‐date on resize
        window.addEventListener('resize', updatePopupPosition);

        return () => {
          cancelAnimationFrame(rafId);
          window.removeEventListener('resize', updatePopupPosition);
        };
      }
    }, [showPostDisconnectModal, showSupabaseActionModal, showPostLinkModal]);

    const openSupabaseActionModal = (actionType: 'create' | 'link' | 'duplicate') => {
      setShowSupabaseActionModal(true);
      setSupabaseActionType(actionType);
      setDisableSendButton(true);
    };

    useEffect(() => {
      if (isStreaming && showSupabaseActionModal) {
        setShowSupabaseActionModal(false);
        setSupabaseActionType(null);
      }
    }, [isStreaming, showSupabaseActionModal]);

    useEffect(() => {
      if (!showSupabaseActionModal && disableSendButton) {
        setShouldDisableOnFirstStream(true);
        setDisableSendButton(false);
      }
    }, [showSupabaseActionModal]);

    useEffect(() => {
      if (isStreaming && shouldDisableOnFirstStream) {
        setDisableSendButton(true);
        setShouldDisableOnFirstStream(false);
      }

      if (!isStreaming && disableSendButton) {
        setDisableSendButton(false);
      }
    }, [isStreaming]);

    const containerRef = useRef<HTMLDivElement>(null);
    const [containerHeight, setContainerHeight] = useState<number | null>(null);
    const [textareaHeight, setTextareaHeight] = useState<number | null>(null);
    const onGrabMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
      if (!chatStarted || !containerRef.current) return;
      e.preventDefault();
      const startY = e.clientY;
      const startH = containerRef.current.offsetHeight;

      const onMouseMove = (ev: MouseEvent) => {
        const delta = ev.clientY - startY;
        const newH = startH - delta;
        if (alreadyOpened && newH < 320) {
          setContainerHeight(320);
        } else if (newH < 220) {
          setContainerHeight(220);
        } else {
          setContainerHeight(newH);
        }
      };
      const onMouseUp = () => {
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
      };

      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
    };

    useEffect(() => {
      if (containerHeight !== null && uploadedFiles.length > 0 && !alreadyOpened) {
        if (containerHeight < 220) {
          setContainerHeight(220);
        } else {
          setContainerHeight((prev) => (prev ?? 0) + 112);
        }
        setAlreadyOpened(true);
      }

      if (uploadedFiles.length === 0 && alreadyOpened) {
        setAlreadyOpened(false);
      }
    }, [uploadedFiles.length]);

    console.log(isModelSelectorOpen ? 'Model selector is open' : 'Model selector is closed');

    const baseChat = (
      <>
        <div className={!chatStarted ? 'container max-w-6xl mx-auto px-4' : 'h-full overflow-hidden'}>
          {plan?.name ? <JoinUsLiveYoutube /> : <UpgradePlanPromotion />}

          {showVerificationModal && <CardSuccessModal onClose={() => setShowVerificationModal(false)} />}
          <StatusPopup
            isOpen={isStatusPopupOpen}
            type={'error'}
            message={t('SupabaseNotAvailable', 'Supabase is not available right now, please try again later.')}
            onClose={() => {
              clearAlert?.();
              setIsStatusPopupOpen(false);
            }}
          />
          <div
            ref={ref}
            className={classNames(
              styles.BaseChat,
              `relative flex h-full w-full overflow-hidden
          ${chatStarted ? 'chat-style-apply ' : 'first-prompt'}`
            )}
            data-chat-visible={showChat}
          >
            <div
              ref={scrollRef}
              className={`flex flex-col ${chatStarted ? 'pt-28' : 'pt-[152px]'}  lg:flex-row overflow-y-auto  w-full h-full ${!chatStarted ? 'lg:flex-row ' : 'max-h-[100vh] new-bg-chat '}`}
            >
              <div
                style={{ zIndex: '1', margin: '0' }}
                className={classNames(
                  styles.Chat,
                  chatStarted ? '' : styles.chatbg,
                  'overflow-y-hidden flex flex-col flex-grow lg:min-w-[var(--chat-min-width)] h-full justify-between entire-chat'
                )}
              >
                {!chatStarted && (
                  <div
                    style={{ zIndex: '1' }}
                    id="intro"
                    className={'flex mx-auto text-center px-4 lg:px-0 flex-col items-center gap-3'}
                  >
                    
                    <motion.a
                      href="https://www.producthunt.com/products/biela-dev?embed=true&utm_source=badge-featured&utm_medium=badge&utm_source=badge-biela&#0045;dev"
                      target="_blank"
                      className={'mb-[100px] max-md:my-6'}
                    >
                      <img
                        src="https://api.producthunt.com/widgets/embed-image/v1/featured.svg?post_id=953262&theme=light&t=1751353227565"
                        alt="Biela&#0046;dev - If&#0032;you&#0032;can&#0032;imagine&#0032;it&#0032;you&#0032;can&#0032;code&#0032;it | Product Hunt"
                        style={{ width: '250px', height: ' 54px' }}
                        width="250"
                        height="54"
                      />
                    </motion.a>
                    {/*   <motion.a
                      href={'https://biela.dev/hackathon'}
                      className={
                        'text-sm border border-[#282828] rounded-full px-4 py-2 hover:bg-[#4ade80] hover:text-black transition-colors mb-[100px] max-md:my-6 flex gap-2 items-center justify-center'
                      }
                    >
                      <FaTrophy size={16} />
                      <span className="text-sm">{t('joinVibeCoding', 'Join Vibe Coding Hackathon')}</span>
                    </motion.a>*/}
                    <motion.h1
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.1 }}
                      style={{ letterSpacing: '-4.5px' }}
                      className="text-3xl max-sm:text-2xl md:text-[30px] font-light mx-auto rexton-light"
                    >
                      {t('whatDoYouWantToBuild', 'WHAT DO YOU WANT TO BUILD?')}
                    </motion.h1>
                    <motion.p
                      className="text-lg md:text-xl font-light mb-[48px]"
                      style={{ color: 'rgb(212, 212, 212)' }}
                    >
                      {t('imaginePromptCreate', ' Imagine. Prompt. Create.')}
                    </motion.p>
                  </div>
                )}
                <div
                  className={classNames('max-w-4xl mx-auto w-full', {
                    ' z-1 show-prompt  h-full flex flex-col bg-[#0A0F1C]': chatStarted,
                  })}
                >
                  <ClientOnly>
                    {() =>
                      chatStarted ? (
                        <>
                          <TabsSection
                            activeTab={activeTab}
                            setActiveTab={setActiveTab}
                            messages={messages!.filter((msg) => msg.role === 'assistant')}
                          />

                          {/* CHAT */}
                          {activeTab === 'chat' && (
                            <>
                              <div className="bg-[#0A0F1C] z-1 chat-tab-section max-[800px]:pr-[55px]">
                                <div className="bg-[#0A0F1C]">
                                  <div className="min-[800px]:max-w-[var(--chat-min-width)] with-border-section flex justify-between border border-white/5 items-center top-header-prompt text-sm px-4 py-2 shadow-xs max-sm:flex-wrap max-sm:gap-2 ">
                                    <div className="flex gap-[6px] items-center">
                                      <WithTooltip
                                        className={'text-[13px] font-normal tracking-[0.4px] !bg-[#1F2937]'}
                                        tooltip={checkingList ? t('checkingFeatures') : t('checklists')}
                                      >
                                        <button
                                          className="flex items-center gap-1.5 px-3 py-1.5 text-white/70 hover:bg-white/5 rounded-md transition-colors text-xs"
                                          style={{
                                            backgroundColor: 'transparent',
                                            display: chatStarted ? 'flex' : 'none',
                                          }}
                                          onClick={handleChecklist}
                                        >
                                          <CheckList checked={checkingList} />
                                          <span className="text-[13px] font-normal tracking-[0.4px]">
                                            {checkingList ? t('checkingFeatures') : t('checklists')}
                                          </span>
                                        </button>
                                      </WithTooltip>
                                      {chatStarted && (
                                        <WithTooltip
                                          tooltip={`${showPrompt ? t('hidePrompt', 'Hide Prompt') : t('showPrompt', 'Show Prompt')}`}
                                          className={'!bg-[#1F2937]'}
                                        >
                                          <button
                                            className={`p-1.5 bg-white/0 hover:bg-white/5  rounded-md transition-transform duration-300 ${showPrompt ? 'rotate-180' : 'rotate-0'}`}
                                            onClick={() => setShowPrompt(!showPrompt)}
                                          >
                                            <ChevronDownIcon className="w-4 h-4 text-white/50" />
                                          </button>
                                        </WithTooltip>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* Chat messages */}
                              <Messages
                                ref={messageRef}
                                className="py-4 flex flex-col w-full flex-1 border-l border-l-white/5 border-r-white/5 max-w-chat mx-auto z-1 gap-messages base-response"
                                isStreaming={isStreaming}
                                messages={(messages as MessageWithHidden[])?.filter((msg) => !msg.hiddenAt)}
                                isTyping={true}
                              />
                            </>
                          )}

                          {/* HISTORY */}
                          {activeTab === 'history' && (
                            <>
                              <div className="min-[800px]:max-w-[var(--chat-min-width)] px-4 py-2 max-[800px]:mr-[55px]">
                                {/* <HistorySwitch /> */}
                              </div>
                              {/*<h2 className="text-xl font-bold text-white">History</h2>*/}
                              <HistoryContent
                                className="flex-1 flex-messages overflow-y-auto pr-3 mb-4  max-[800px]:mr-[55px]"
                                messages={messages!.filter((msg) => msg.role === 'assistant')}
                              />
                            </>
                          )}

                          {/* ROADMAP */}
                          {activeTab === 'roadmap' && (
                            <div className="roadmap-tab h-full z-1 bg-[#0A0F1C] max-[800px]:pr-[55px]">
                              <div className="min-[800px]:max-w-[var(--chat-min-width)] px-4 py-2 border border-white/5 max-[800px]:mr-[55px]">
                                <RoadmapSwitch />
                              </div>
                              <RoadmapContent />
                            </div>
                          )}
                        </>
                      ) : null
                    }
                  </ClientOnly>

                  <ClientOnly>
                    {() => (
                      <div
                        ref={containerRef}
                        style={{
                          zIndex: '1',
                          '--rotaion-variable': `${rotation}deg`,
                          height: containerHeight != null ? `${containerHeight}px` : undefined,
                        }}
                        className={classNames(
                          `${(actionAlert?.type === 'preview' || actionAlert?.type === 'migration' || actionAlert?.type === 'error') && 'chat-on-error'} flex flex-col place-items-center align-items-center justify-center rounded-lg relative w-full max-w-[100vw] mx-auto z-prompt base-chat`,
                          {
                            'sticky bottom-2': chatStarted,
                            'chat-mode': mode === 'chat',
                          }
                        )}
                        data-chat-visible={showChat}
                      >
                        {chatStarted && (
                          <>
                            <div
                              onMouseDown={!isModelSelectorOpen ? onGrabMouseDown : undefined}
                              className={`min-h-[7px] w-full resize-handle ${isModelSelectorOpen ? 'z-1' : 'z-10'}`}
                            ></div>
                            <div className={classNames(styles.containerUnitTesting, 'w-full absolute bottom-[100%]')}>
                              {!automaticError ? (
                                <ErrorHandler
                                  onAskBiela={(message) => {
                                    setChatMode('code');
                                    sendMessage?.({} as React.UIEvent, message, { annotations: ['once'] });
                                    clearAlert?.();
                                  }}
                                  onDiscussProblem={(message) => {
                                    setChatMode('chat');
                                    sendMessage?.({} as React.UIEvent, message, { annotations: ['once'] });
                                    clearAlert?.();
                                  }}
                                  onIgnore={() => {
                                    clearAlert?.();
                                  }}
                                />
                              ) : (
                                <ActionAlertCollect
                                  isStreaming={isStreaming}
                                  actionAlert={actionAlert}
                                  sendMessage={sendMessage}
                                  clearAlert={clearAlert}
                                  maxErrorsToTrack={10}
                                  batchFixDelay={3000}
                                />
                              )}

                              {actionAlert?.type === 'unitTesting' && !isStreaming && (
                                <ChatSuggestion
                                  title={t('runUnitTestsSuggestionTitle', 'Suggestion')}
                                  message={t(
                                    'runUnitTestsSuggestionMessage',
                                    'Would you like to Run Unit Tests for your project?'
                                  )}
                                  primaryButtonText={t('runUnitTestsPrimaryButton', 'Run Unit Tests')}
                                  secondaryButtonText={t('runUnitTestsSecondaryButton', 'Dismiss')}
                                  clearAlert={() => clearAlert?.()}
                                  postMessage={() => {
                                    sendMessage?.({} as React.UIEvent, 'Run Unit Tests for my Project');
                                    clearAlert?.();
                                  }}
                                />
                              )}
                              {actionAlert?.type === 'database' && !isStreaming && (
                                <ChatSuggestion
                                  title={t('createDatabaseTitle', 'Database Creation')}
                                  message={t(
                                    'createDatabaseMessage',
                                    'Would you like to create a database for your project?'
                                  )}
                                  primaryButtonText={t('createDatabasePrimaryButton', 'Create Database')}
                                  secondaryButtonText={t('createDatabaseSecondaryButton', 'Dismiss')}
                                  clearAlert={() => clearAlert?.()}
                                  postMessage={() => {
                                    sendMessage?.({} as React.UIEvent, 'Create a database for my project');
                                    clearAlert?.();
                                  }}
                                />
                              )}
                            </div>
                          </>
                        )}

                        <AnimatePresence>
                          {(showPostDisconnectModal || showSupabaseActionModal || showPostLinkModal) && (
                            <motion.div
                              ref={popupRef}
                              initial={{
                                opacity: 0,
                                x: popupStyle.left,
                                y: popupStyle.top,
                                zIndex: 1,
                              }}
                              animate={{
                                opacity: 1,
                                scale: 1,
                                x: popupStyle.left,
                                y: popupStyle.top - popupHeight,
                                zIndex: 1,
                              }}
                              exit={{
                                opacity: 0,
                                x: popupStyle.left,
                                y: popupStyle.top,
                                zIndex: 1,
                              }}
                              transition={{ duration: 0.4, ease: 'easeInOut' }}
                              style={{
                                position: 'absolute',
                                width: popupStyle.width,
                                left: 0,
                                top: 0,
                                pointerEvents: 'auto',
                                zIndex: 1,
                              }}
                              className="rounded-lg bg-[#0A0F1C]"
                            >
                              {showPostDisconnectModal ? (
                                <PostDisconnectProjectModal
                                  isOpen={showPostDisconnectModal}
                                  onClose={() => setShowPostDisconnectModal(false)}
                                  onConfirm={(selected) => {
                                    setShowPostDisconnectModal(false);
                                    sendMessage?.(
                                      {} as React.UIEvent,
                                      `I have disconnected the Supabase project from this application. Please ${selected}`,
                                      { annotations: ['hidden'] }
                                    );
                                  }}
                                />
                              ) : showPostLinkModal ? (
                                <PostLinkProjectModal
                                  isOpen={showPostLinkModal}
                                  databaseName={pendingLinkData?.databaseName || ''}
                                  onClose={() => {
                                    setShowPostLinkModal(false);
                                    setDisableSendButton(false);
                                  }}
                                  onConfirm={async (selected) => {
                                    setShowPostLinkModal(false);
                                    setDisableSendButton(false);
                                    pendingLinkData?.onSuccess?.(
                                      selected,
                                      pendingLinkData.fetchDatabaseSchemaAndSendToAI
                                    );
                                  }}
                                />
                              ) : (
                                <SupabaseActionInProgressModal
                                  isOpen={showSupabaseActionModal}
                                  actionType={supabaseActionType}
                                />
                              )}
                            </motion.div>
                          )}
                        </AnimatePresence>
                        <div ref={chatRef} className="w-[100%] h-full rotate-full relative group z-2 flex flex-col">
                          <FilePreview
                            files={uploadedFiles}
                            imageDataList={imageDataList}
                            onRemove={(index) => {
                              removeContentURLFromPrompt?.(uploadedFiles.filter((_, i) => i === index)[0]);
                              setUploadedFiles?.(uploadedFiles.filter((_, i) => i !== index));
                              setImageDataList?.(imageDataList.filter((_, i) => i !== index));
                            }}
                          />
                          <ClientOnly>
                            {() => (
                              <ScreenshotStateManager
                                setUploadedFiles={setUploadedFiles}
                                setImageDataList={setImageDataList}
                                uploadedFiles={uploadedFiles}
                                imageDataList={imageDataList}
                              />
                            )}
                          </ClientOnly>

                          <div
                            className={classNames(
                              { 'p-1 py-4 md:p-8 show-prompt': !chatStarted },
                              `flex-1 relative transition-all flex flex-col  md:block ${!showPrompt ? '' : 'hide-prompt'}`
                            )}
                          >
                            <div
                              className={classNames({
                                'chat-default flex flex-col md:flex-row items-center gap-2': !chatStarted,
                              })}
                            >
                              {(!messages || messages.length === 0) && (
                                <>
                                  {!loadingFromBackend && (
                                    <>
                                      <ChatCodeSwitch
                                        activeTab={activeTab}
                                        changeTabToChat={() => {
                                          setActiveTab('chat');
                                        }}
                                      />
                                      <AIModelSelector onOpenChange={setIsModelSelectorOpen} />
                                    </>
                                  )}
                                </>
                              )}
                            </div>
                            <div className="h-full show-prompt border w-full border-white/5 flex flex-col gap-4 py-4 px-0.5 md:py-4 md:px-4 !bg-[#00000008]">
                              <div
                                className={classNames(
                                  `container-textarea h-full ${!chatStarted && 'max-h-[88px]'} border border-white/5 hover:border-white/10 transition-all hover:border-biela-elements-focus duration-200 text-white transition-colors rounded-lg bg-transparent`,
                                  {
                                    'border border-white/5': !isDragActive, // show borders only when NOT dragging
                                    dragging: isDragActive, // add “dragging” when active
                                  }
                                )}
                              >
                                <div
                                  className={classNames(styles.dropZone, { [styles.dragActive]: isDragActive })}
                                  /* …handlers… */
                                  style={{ position: 'relative', height: '100%' }}
                                  onDragEnter={() => setIsDragActive(true)}
                                  onDragOver={(e) => {
                                    e.preventDefault();
                                    // e.currentTarget.style.background = '#1488fc';
                                    e.currentTarget.style.borderRadius = '9px';
                                    setIsDragActive(true);
                                  }}
                                  // onDragLeave={() => setIsDragActive(false)}

                                  onDragLeave={(e) => {
                                    setIsDragActive(false);
                                    e.currentTarget.style.background = 'none';
                                  }}
                                  onDrop={(e) => {
                                    e.preventDefault();
                                    e.currentTarget.style.background = 'none';
                                    e.stopPropagation();
                                    setIsDragActive(false); // <<< this was missing
                                  }}
                                >
                                  {isDragActive && (
                                    <div
                                      className="box absolute inset-0 flex items-center justify-center
                                                 rounded-lg pointer-events-none"
                                    >
                                      <span className="text-white text-sm font-medium">Drop files to upload</span>
                                    </div>
                                  )}
                                  <textarea
                                    rows={3}
                                    ref={textareaRef}
                                    className={classNames(
                                      { '': !chatStarted },
                                      'w-full p-4 !overflow-auto h-full outline-none resize-none text-biela-elements-textPrimary placeholder-biela-elements-textTertiary bg-transparent text-sm chat-textarea-font',
                                      ' duration-200',
                                      'hover:border-biela-elements-focus'
                                    )}
                                    value={inputValue}
                                    onDragEnter={(e) => {
                                      e.preventDefault();
                                    }}
                                    onDragOver={(e) => {
                                      e.preventDefault();
                                    }}
                                    onDragLeave={(e) => {
                                      e.preventDefault();
                                    }}
                                    onDrop={(e) => {
                                      e.preventDefault();
                                      const files = Array.from(e.dataTransfer.files);
                                      files.forEach((file) => {
                                        if (file.type.startsWith('image/')) {
                                          const reader = new FileReader();
                                          reader.onload = async (e) => {
                                            const base64Image = e.target?.result as string;
                                            const img = new Image();
                                            img.onload = () => {
                                              const canvas = document.createElement('canvas');
                                              canvas.width = img.width;
                                              canvas.height = img.height;
                                              const ctx = canvas.getContext('2d');
                                              if (ctx) {
                                                ctx.drawImage(img, 0, 0);
                                                const processedImage = canvas.toDataURL(file.type, 1.0);
                                                pushFile(file, processedImage);
                                              }
                                            };
                                            img.onerror = (error) => {
                                              console.error('Error processing image:', error);
                                            };
                                            img.src = base64Image;
                                          };
                                          reader.readAsDataURL(file);
                                        }
                                      });
                                    }}
                                    onKeyDown={(event) => {
                                      if (event.key === 'Enter') {
                                        if (event.shiftKey) return;
                                        event.preventDefault();
                                        if (isStreaming) {
                                          handleStop?.();
                                          return;
                                        }
                                        if (event.nativeEvent.isComposing) return;
                                        handleSendMessage?.(event);
                                      }
                                    }}
                                    onKeyDownCapture={(event) => {
                                      if (event.key === 'Enter' && isStreaming) {
                                        event.preventDefault();
                                        event.stopPropagation();
                                        return;
                                      }
                                    }}
                                    onChange={(event) => {
                                      setInputValue(event.target.value);
                                      handleInputChange?.(event);
                                    }}
                                    onPaste={handlePaste}
                                    placeholder={isDragActive ? '' : placeholderText}
                                    translate="no"
                                  />
                                </div>
                              </div>

                              <div className="flex justify-between items-center text-sm prompt-actions-button shadow-xs h-[52px]">
                                <ActionButtons
                                  onFileClick={handleFileUpload}
                                  isListening={isListening}
                                  onStartVoice={startListening}
                                  onStopVoice={stopListening}
                                  enhancePrompt={enhancePrompt}
                                  input={input}
                                  enhancingPrompt={enhancingPrompt}
                                  handleCleanUp={handleCleanUp}
                                  chatStarted={chatStarted}
                                  hasContent={hasContent}
                                  hasHistory={hasHistory}
                                  onModelSelectorOpenChange={setIsModelSelectorOpen}
                                />
                                <ClientOnly>
                                  {() => (
                                    <SendButton
                                      // show={!!input.length || isStreaming || !!uploadedFiles.length}
                                      show={true}
                                      isStreaming={isStreaming}
                                      hasContent={hasContent}
                                      disabled={disableSendButton || loadingFromBackend}
                                      onClick={(event) => {
                                        if (isStreaming) {
                                          abort();
                                          return;
                                        }
                                        if (inputValue.length || uploadedFiles.length) {
                                          setTryToRetry(false);
                                          handleSendMessage?.(event, inputValue);
                                        }
                                      }}
                                      type={!isStreaming ? t('sendButton') : t('abortButton')}
                                    />
                                  )}
                                </ClientOnly>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </ClientOnly>
                </div>
                {/*{!chatStarted && <CopyRighting />}*/}
              </div>
              <Suspense>
                {chatStarted && (
                  <Workbench
                    isTokenDialogOpen={isTokenDialogOpen}
                    onBuyTokens={onBuyTokens}
                    onCloseTokenDialog={onCloseTokenDialog}
                    hasTokens={hasTokens}
                    sendMessage={sendMessage}
                    chatStarted={chatStarted}
                    isStreaming={isStreaming}
                    mode={mode}
                    uploadContentStudioImages={simulateMultipleResourcesDrops}
                    openPostDisconnectModal={() => setShowPostDisconnectModal(true)}
                    openSupabaseActionModal={openSupabaseActionModal}
                    openPostLinkModal={openPostLinkModal}
                  />
                )}
              </Suspense>
            </div>
          </div>
        </div>
        {!chatStarted && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="mb-12 px-4 container mx-auto px-6 z-10"
          >
            <ProjectIdeasCarousel setPrompt={handleIdeaClick} />
          </motion.div>
        )}
      </>
    );

    return <Tooltip.Provider delayDuration={200}>{baseChat}</Tooltip.Provider>;
  }
);
