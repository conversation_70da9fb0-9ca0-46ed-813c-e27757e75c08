import { atom } from 'nanostores';
import { backendApiFetch } from '../../backend-api';

type UserPreferencesState = {
  affiliateDailyRewardsPopup: boolean;
  automaticError: boolean;
  affiliateDailyRewardsHowToPopup: boolean;
  createdAt: Date;
  developerMode: boolean;
  updatedAt: Date;
  aiDiffMode: boolean;
};

class ControlCenterStore {
  affiliateDailyRewardsHowToPopup = atom<boolean>(false);
  affiliateDailyRewardsPopup = atom<boolean>(false);
  automaticError = atom<boolean>(false);
  developerMode = atom<boolean>(false);
  aiDiffMode = atom<boolean>(false);
  private isInitialized = false;

  constructor() {
    this.affiliateDailyRewardsHowToPopup.listen(this.saveState.bind(this));
    this.affiliateDailyRewardsPopup.listen(this.saveState.bind(this));
    this.automaticError.listen(this.saveState.bind(this));
    this.developerMode.listen(this.saveState.bind(this));
    this.aiDiffMode.listen(this.saveState.bind(this));
    this.fetchUserPreferences();
  }

  private fetchUserPreferences = async () => {
    try {
      const response = await backendApiFetch('/user/preferences', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        return;
      }

      const data: UserPreferencesState = await response.json();
      this.affiliateDailyRewardsHowToPopup.set(data.affiliateDailyRewardsHowToPopup);
      this.affiliateDailyRewardsPopup.set(data.affiliateDailyRewardsPopup);
      this.automaticError.set(data.automaticError);
      this.developerMode.set(data.developerMode);
      this.aiDiffMode.set(data.aiDiffMode);
    } catch {
    } finally {
      this.isInitialized = true;
    }
  };

  private async saveState() {
    if (!this.isInitialized) {
      return;
    }

    try {
      await backendApiFetch('/user/preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          affiliateDailyRewardsHowToPopup: this.affiliateDailyRewardsHowToPopup.get(),
          affiliateDailyRewardsPopup: this.affiliateDailyRewardsPopup.get(),
          developerMode: this.developerMode.get(),
          automaticError: this.automaticError.get(),
          aiDiffMode: this.aiDiffMode.get(),
        }),
      });
    } catch {}
  }

  // Add methods to manage the control center state
}

export const controlCenterStore = new ControlCenterStore();