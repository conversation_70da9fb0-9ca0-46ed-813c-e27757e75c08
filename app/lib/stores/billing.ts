import { UserStore } from '~/ai/lib/stores/user/userStore';
import { backendApiFetch } from '~/ai/lib/backend-api';
import {
  CheckoutSessionMode,
  ICouponProps,
  Invoice,
  InvoiceData,
  PaginatedResponse,
  Plan,
  TopUp,
} from '~/types/billing';

export const createCheckoutSession = async ({
  mode,
  planId = '',
  priceId = '',
  tokens = '',
  couponCode = '',
}: CheckoutSessionMode): Promise<void> => {
  try {
    const token = UserStore.getInstance().getToken() || '';
    let requestBody: Record<string, any> = {};

    if (mode === 'payment') {
      requestBody = {
        tokens: tokens,
        currency: 'usd',
        successUrl: `${window.location.origin}/billing?paymentType=topup`,
        cancelUrl: `${window.location.origin}/billing`,
        mode: 'payment',
        couponCode,
      };
    } else {
      requestBody = {
        successUrl: `${window.location.origin}/billing?paymentType=subscription`,
        cancelUrl: `${window.location.origin}/billing`,
        mode: 'subscription',
        priceId,
        planId,
        couponCode,
      };
    }

    const response = await backendApiFetch('/payments/stripe/create-portal-session', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'user-token': token,
      },
      body: JSON.stringify(requestBody),
    });

    const data = (await response.json()) as { sessionUrl: string };

    if (data.sessionUrl) {
      window.location.href = data.sessionUrl;
    } else {
      throw new Error('No checkout URL returned');
    }
  } catch (error) {
    console.error('Error creating checkout session:', error);
    throw error;
  }
};

export const fetchPlans = async (billingCycle: string): Promise<Plan[]> => {
  const response = await backendApiFetch(`/plans/plans-period?billingCycle=${billingCycle}&includeActive=true`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Failed to fetch plans');
  }

  return response.json();
};

export const fetchTopUps = async (): Promise<TopUp> => {
  const response = await backendApiFetch('/plans/get-topUp', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Failed to fetch top-ups');
  }

  return response.json();
};

export const fetchInvoices = async (currentPage: number, pageSize: number): Promise<PaginatedResponse<Invoice>> => {
  const response = await backendApiFetch(
    `/payments/stripe/payments-invoices?page=${currentPage}&pageSize=${pageSize}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  if (!response.ok) {
    throw new Error('Failed to fetch invoices');
  }

  return response.json();
};

export interface SubscriptionPaymentResult {
  date: Date;
  amount: number;
  tokens: number;
  planName: string;
}

export interface TopupPaymentResult {
  date: Date;
  amount: number;
  tokens: number;
  couponUsed?: string;
}

export type PaymentResult = SubscriptionPaymentResult | TopupPaymentResult | null;

export const fetchLastReceipt = async (paymentType: string): Promise<PaymentResult> => {
  const response = await backendApiFetch(`/payments/meta/get-last-receipt?paymentType=${paymentType}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Failed to fetch plans');
  }

  return response.json();
};

export const fetchCurrentUserPlan = async (): Promise<Plan | null> => {
  const response = await backendApiFetch('/plans/get-current-user-plan', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Failed to fetch current user plan');
  }

  return response.json();
};

export const generateInvoicePDF = async (
  invoiceId: string,
  paymentType: 'subscription' | 'topup' | 'crypto',
  paymentId?: string
): Promise<InvoiceData> => {
  const response = await backendApiFetch(`/payments/stripe/generate-invoice-pdf?paymentType=${paymentType}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ invoiceId, paymentId }),
  });

  if (!response.ok) {
    throw new Error('Failed to generate invoice PDF');
  }

  return response.json();
};

export const checkCouponCode = async (couponCode: string, register = false): Promise<ICouponProps> => {
  const response = await backendApiFetch(
    `/admin/coupon/check?couponCode=${couponCode}&type=payments&register=${register}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );

  if (!response.ok) {
    throw new Error('Failed to check coupon code');
  }

  return response.json();
};

export const getPreAppliedCode = async () => {
  const response = await backendApiFetch(`/admin/coupon/preapplied`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Failed to retrieve preapplied coupon');
  }

  return response.json();
};

export const consumePreAppliedCode = async (code: string) => {
  const response = await backendApiFetch(`/admin/coupon/consume-preapplied/${code}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('Failed to retrieve preapplied coupon');
  }

  return response;
};
``;
