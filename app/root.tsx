import React, { lazy, Suspense, useCallback, useEffect, useState } from 'react';
import { BrowserRouter, Routes, Route, useLocation, Navigate } from 'react-router-dom';
import { UserProvider, useUser } from './ai/lib/context/userContext';
import { I18nextProvider, useTranslation } from 'react-i18next';
import i18n from '../i18n';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './components/styles/font.scss';
import './components/styles/index.scss';
import '@xterm/xterm/css/xterm.css';
import 'virtual:uno.css';

import SingleTabEnforcer from './ai/components/SingleTabEnforcer';
import { ChatOpenProvider } from './ai/lib/context/chatOpenContext';
import { MaintenanceProvider } from './ai/lib/context/maintenanceContext';

import { createRoot } from 'react-dom/client';
import LoadFailurePage from './routes/Load-Failure-Page';
import ConfirmEmail from './routes/confirm-email';
import { backendApiFetch } from './ai/lib/backend-api';
import ToastContainerCustom from '~/ai/components/ToastContainer';
import ConsentBanner from './components/consentbanner/ConsentBanner';
import ProjectShare from '~/routes/project-share';

// Lazy imports
const Index = lazy(() => import('./routes/_index'));
const Auth = lazy(() => import('./routes/$auth'));
const AccessDeinedPage = lazy(() => import('./routes/Access-Deined-Page'));
const ChatId = lazy(() => import('./routes/chat.$id'));
const Dashboard = lazy(() => import('./routes/dashboard'));
const GetStarted = lazy(() => import('./routes/get-started'));
const Help = lazy(() => import('./routes/help'));
const Logout = lazy(() => import('./routes/logout'));
const NotFoundPage = lazy(() => import('./routes/Not-Found-Page'));
// const PrivacyInfo = lazy(() => import('./routes/privacy.info'));
// const Privacy = lazy(() => import('./routes/privacy'));
// const PrivacyPolicy = lazy(() => import('./routes/privacy-policy'));
const Profile = lazy(() => import('./routes/profile'));
//const SettingsBilling = lazy(() => import('./routes/settings.billing'));
// const SettingsDeployment = lazy(() => import('./routes/settings.deployment'));
const SettingsIdentity = lazy(() => import('./routes/settings.identity'));
// const SettingsProfile = lazy(() => import('./routes/settings.profile'));
//const Settings = lazy(() => import('./routes/settings'));
const Billing = lazy(() => import('./routes/billing'));
const SupabaseCallback = lazy(() => import('./routes/supabase-callback'));
// const TermsOfUseInfo = lazy(() => import('./routes/terms-of-use.info'));
// const TermsOfUse = lazy(() => import('./routes/terms-of-use'));
// const Terms = lazy(() => import('./routes/terms'));
const Tokens = lazy(() => import('./routes/tokens'));
const ResetData = lazy(() => import('./routes/reset-data'));
const ENV_GTM_ID = import.meta.env.VITE_GOOGLE_TAG_MANAGER_ID;
const GTM_ID = ENV_GTM_ID || (import.meta.env.DEV ? 'GTM-5BXQMVKQ' : 'GTM-KG6R9X3G');
function AppRoutes() {
  return (
    <Suspense fallback={<div></div>}>
      <Routes>
        <Route path="/" element={<Index />} />
        <Route path="/dashboard" element={<PrivateRoute element={<Dashboard />} />} />
        <Route path="/chat/:id" element={<PrivateRoute element={<ChatId />} />} />
        <Route path="/supabase-callback" element={<SupabaseCallback />} />
        <Route path="/confirm-email" element={<ConfirmEmail />} />
        {/* <Route path="/settings/profile" element={<SettingsProfile />} /> */}
        {/* <Route path="/settings/billing" element={<SettingsBilling />} />*/}
        {/* <Route path="/settings/deployment" element={<SettingsDeployment />} /> */}
        {/*<Route path="/settings/identity" element={<SettingsIdentity />} />*/}
        {/*<Route path="/settings" element={<Settings />} />*/}
        <Route path="/billing" element={<PrivateRoute element={<Billing />} />} />
        {/* <Route path="/terms-of-use" element={<TermsOfUse />} /> */}
        {/* <Route path="/terms-of-use/info" element={<TermsOfUseInfo />} /> */}
        {/* <Route path="/terms" element={<Terms />} /> */}
        {/* <Route path="/privacy" element={<Privacy />} /> */}
        {/* <Route path="/privacy/info" element={<PrivacyInfo />} /> */}
        {/* <Route path="/privacy-policy" element={<PrivacyPolicy />} /> */}
        <Route path="/logout" element={<Logout />} />
        <Route path="/tokens" element={<Tokens />} />
        <Route path="/access-denied" element={<AccessDeinedPage />} />
        <Route path="/profile" element={<PrivateRoute element={<Profile />} />} />
        <Route path="/help" element={<Help />} />
        <Route path="/get-started" element={<GetStarted />} />
        <Route path="/reset-data/all" element={<ResetData />} />
        <Route path="/:auth-provider" element={<Auth />} />
        <Route path="*" element={<NotFoundPage />} />
        <Route path="/project-share" element={<ProjectShare />} />
      </Routes>
      <ConsentBanner />
    </Suspense>
  );
}

function AppContent({ setUserEmail }: { setUserEmail: (email?: string) => void }) {
  const location = useLocation();
  const { authenticateToken, user } = useUser();
  useEffect(() => {
    const root = document.documentElement;
    const fs = parseFloat(getComputedStyle(root).fontSize);
    let headerHeight = '90px';

    if (fs > 16 && fs < 24) {
      headerHeight = '100px';
    } else if (fs >= 24) {
      headerHeight = '120px';
    }

    root.style.setProperty('--header-height', headerHeight);
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams(location.search);
      const referral = params.get('referral');
      const tokenFrontOffice = params.get('token');
      if (referral) {
        localStorage.setItem('referral', referral);
      }
      if (tokenFrontOffice && !user) {
        authenticateToken(tokenFrontOffice);
      }
    }
  }, [location.search]);

  useEffect(() => {
    if (!GTM_ID) return;

    (window as any).dataLayer = (window as any).dataLayer || [];
    (window as any).dataLayer.push({
      'gtm.start': new Date().getTime(),
      event: 'gtm.js',
    });

    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtm.js?id=${GTM_ID}`;
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, []);

  return (
    <>
      <AppRoutes />
      <ToastContainerCustom />
      {/*<ToastContainer theme="dark" />*/}
    </>
  );
}

class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { error: any }> {
  constructor(props: any) {
    super(props);
    this.state = { error: null };
  }
  static getDerivedStateFromError(error: any) {
    return { error };
  }
  componentDidCatch(error: any, info: any) {
    console.error(error, info);
  }
  render() {
    const { error } = this.state;

    if (error) {
      if (error.status === 404) {
        return <NotFoundPage />;
      }

      if (error.status === 403) {
        return <AccessDeinedPage />;
      }

      if (error.status === 500) {
        return <LoadFailurePage />;
      }

      return <LoadFailurePage />;
    }

    return this.props.children;
  }
}

function PrivateRoute({ element }: { element: JSX.Element }) {
  const { isLoggedIn } = useUser();
  const location = useLocation();

  if (!isLoggedIn()) {
    return <Navigate to={`/?redirectUrl=${encodeURIComponent(location.pathname)}`} replace />;
  }

  return element;
}

export default function App() {
  const [userEmail, setUserEmail] = useState<string>();

  return (
    <ErrorBoundary>
      {/*<MaintenanceProvider>*/}
      <ChatOpenProvider>
        <UserProvider>
          <I18nextProvider i18n={i18n}>
            <SingleTabEnforcer />
            <AppContent setUserEmail={setUserEmail} />
          </I18nextProvider>
        </UserProvider>
      </ChatOpenProvider>
      {/*</MaintenanceProvider>*/}
    </ErrorBoundary>
  );
}

const container = document.getElementById('root');
const root = createRoot(container!);
root.render(
  <BrowserRouter>
    <App />
  </BrowserRouter>
);
