import React, { useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import { PlanCard } from './PlanCard';
import { PlanCardSkeleton } from './PlanCardSkeleton';
import { NoPlan } from './NoPlan';
import { PlansListProps } from '~/types/billing';
import { createCheckoutSession } from '~/lib/stores/billing';
import CouponInput, { Coupon } from './CouponInput';
import { ValueType } from './helper';

export const PlansList: React.FC<PlansListProps> = ({ plans, loading, isMobile, billingCycle }) => {
  const [currentPlanIndex, setCurrentPlanIndex] = useState<number>(0);
  const [appliedCoupon, setAppliedCoupon] = useState<Coupon | null>(null);

  const nextPlan = (): void => {
    setCurrentPlanIndex((prev) => (prev === plans.length - 1 ? 0 : prev + 1));
  };

  const prevPlan = (): void => {
    setCurrentPlanIndex((prev) => (prev === 0 ? plans.length - 1 : prev - 1));
  };

  const handleApplyCoupon = (coupon: Coupon | null) => {
    setAppliedCoupon(coupon);
  };

  const handleSubscribe = async (planId: string, priceId: string): Promise<void> => {
    // Only allow subscription discount coupons for subscriptions
    const couponCode = appliedCoupon && appliedCoupon.valueType === ValueType.PERCENTAGE_SUBSCRIPTION_DISCOUNT 
      ? appliedCoupon.code 
      : '';

    await createCheckoutSession({
      mode: 'subscription',
      planId,
      priceId,
      couponCode,
    });
  };

  if (isMobile && plans?.length > 0) {
    return (
      <>
        {/* Coupon Input Section for Mobile */}
        <div className="max-w-md mx-auto mb-8">
          <CouponInput 
            onApplyCoupon={handleApplyCoupon} 
            currentCoupon={appliedCoupon} 
          />
        </div>

        <div className="relative">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentPlanIndex}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              transition={{ duration: 0.3 }}
              className="w-full"
            >
              {plans.length > 0 && (
                <PlanCard
                  plan={plans[currentPlanIndex]}
                  index={currentPlanIndex}
                  billingCycle={billingCycle}
                  onSubscribe={handleSubscribe}
                  isMobile={isMobile}
                  appliedCoupon={appliedCoupon}
                />
              )}
            </motion.div>
          </AnimatePresence>

          <div className="flex justify-center gap-4 mt-6">
            <button onClick={prevPlan} className="p-2 bg-gray-800/50 rounded-full hover:bg-gray-700/50 transition-colors">
              <FaChevronLeft />
            </button>
            <div className="flex gap-2 mt-3">
              {plans.map((_, idx) => (
                <button
                  key={idx}
                  onClick={() => setCurrentPlanIndex(idx)}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    currentPlanIndex === idx ? 'bg-green-500' : 'bg-gray-600'
                  }`}
                />
              ))}
            </div>
            <button onClick={nextPlan} className="p-2 bg-gray-800/50 rounded-full hover:bg-gray-700/50 transition-colors">
              <FaChevronRight />
            </button>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      {/* Coupon Input Section */}
      {!loading && plans?.length > 0 && (
        <div className="max-w-md mx-auto mb-8">
          <CouponInput 
            onApplyCoupon={handleApplyCoupon} 
            currentCoupon={appliedCoupon} 
          />
        </div>
      )}

      <div
        className={`${
          plans?.length === 0 ? 'flex gap-6' : 'grid grid-cols-1 md:grid-cols-3 gap-6 mb-8'
        } items-stretch justify-center`}
      >
        {loading ? (
          [0, 1, 2].map((index) => <PlanCardSkeleton key={index} index={index} />)
        ) : plans?.length === 0 ? (
          <NoPlan />
        ) : (
          plans.map((plan, index) => (
            <PlanCard
              key={plan.id}
              plan={plan}
              index={index}
              billingCycle={billingCycle}
              onSubscribe={handleSubscribe}
              isMobile={isMobile}
              appliedCoupon={appliedCoupon}
            />
          ))
        )}
      </div>
    </>
  );
};
