import React from 'react';
import { motion } from 'framer-motion';
import { FaGift, FaInfinity, FaRobot } from 'react-icons/fa';
import { PlanCardProps } from '~/types/billing';
import { AVAILABLE_ICONS, FeatureDescriptions, formatNumber, ValueType } from '~/backOffice/components/billings/components/helper';
import { JSX } from 'react/jsx-runtime';
import { toNumber } from 'lodash';
import { useTranslation } from 'react-i18next';

export const PlanCard: React.FC<PlanCardProps> = ({ plan, index, billingCycle, onSubscribe, isMobile, appliedCoupon }) => {
  const { t } = useTranslation('profile');
  const periodMultiplier = billingCycle === 'yearly' ? 12 : 1;
  const monthlyTokens = toNumber(plan.tokenAllocation);
  
  // Calculate discounted price if coupon is applied and valid for subscriptions
  const originalPrice = plan.price;
  const isSubscriptionDiscount = appliedCoupon?.valueType === ValueType.PERCENTAGE_SUBSCRIPTION_DISCOUNT;
  const discountedPrice = isSubscriptionDiscount 
    ? originalPrice * (1 - (appliedCoupon.value / 100))
    : originalPrice;
  
  const pricePerMillion = ((discountedPrice / (monthlyTokens * periodMultiplier)) * 1_000_000).toFixed(2);

  const getIcon = (iconName: string): JSX.Element | undefined => {
    const found = AVAILABLE_ICONS.find((item) => item.name === iconName);
    if (found) {
      const IconComponent = found.icon;
      return <IconComponent />;
    }
    return undefined;
  };

  // Plan descriptions
  const getPlanDescription = (type: string): string => {
    switch (type) {
      case 'beginner':
        return t(
          'planCard.descriptionBeginner',
          'Perfect for beginners building their first projects and learning web development'
        );
      case 'creator':
        return t(
          'planCard.descriptionCreator',
          'Ideal for creators who need more power and flexibility for advanced projects'
        );
      case 'professional':
        return t(
          'planCard.descriptionProfessional',
          'Complete solution for professional developers and teams building applications'
        );
      default:
        return '';
    }
  };

  const isUnlimitedPlan = index === 2;
  return (
    <motion.div
      className={`w-full ${index === 1 && !isMobile ? 'pt-0' : 'pt-12'}`}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <div
        className={`bg-gradient-to-r rounded-xl ${
          plan?.isPopular ? 'ring-2 ring-green-500' : ''
        } h-full flex flex-col overflow-hidden`}
        style={{
          backgroundImage: `linear-gradient(to right, ${plan.colorFrom}, ${plan.colorTo})`,
        }}
      >
        <div className={`p-6 relative ${index === 1 && !isMobile ? 'py-10' : ''}`}>
          {plan?.isPopular && (
            <div className="absolute top-0 right-0">
              <div className="bg-green-500 px-4 py-1 rounded-bl-lg text-sm font-light">
                {t('planCard.mostPopular', 'Most Popular')}
              </div>
            </div>
          )}
          {!isUnlimitedPlan && (
            <div className={'absolute top-4 left-4'}>
              <div
                className={
                  'flex items-center gap-2 bg-green-500/20 border border-green-500/50 rounded-full px-3 py-1.5'
                }
              >
                <FaGift className={'text-green-400 text-sm'} />
                <span className={'text-green-400 text-sm font-medium'}>
                  {t('freeTokens', '200k Free Tokens', { tokens: '200k' })}
                </span>
              </div>
            </div>
          )}

          <div className="z-10 flex items-center gap-4 mt-8">
            <div className={`p-4 bg-black/20 rounded-xl scale-110 text-2xl`}>{getIcon(plan.icon)}</div>
            <div>
              <h3 className={`font-light ${index === 1 ? 'text-[24px]' : 'text-[20px]'}`}>{plan.name}</h3>
              <div className="flex items-baseline gap-1 mt-1">
                {isSubscriptionDiscount ? (
                  <div className="flex flex-col">
                    <div className="flex items-baseline gap-1">
                      <span className={`font-light text-gray-400 line-through ${index === 1 ? 'text-[24px]' : 'text-[20px]'}`}>
                        $ {originalPrice.toLocaleString('en-US')}
                      </span>
                    </div>
                    <div className="flex items-baseline gap-1">
                      <span className={`font-light text-green-400 ${index === 1 ? 'text-[36px]' : 'text-[30px]'}`}>
                        $ {discountedPrice.toLocaleString('en-US')}
                      </span>
                      <span className="text-gray-400 text-sm font-light">
                        /{billingCycle === 'monthly' ? 'month' : 'year'}
                      </span>
                      <div className="ml-2 bg-green-500/20 border border-green-500/50 rounded px-2 py-1">
                        <span className="text-green-400 text-xs font-medium">
                          -{appliedCoupon?.value}%
                        </span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <>
                    <span className={`font-light ${index === 1 ? 'text-[36px]' : 'text-[30px]'}`}>
                      $ {plan.price.toLocaleString('en-US')}
                    </span>
                    <span className="text-gray-400 text-sm font-light">
                      /{billingCycle === 'monthly' ? 'month' : 'year'}
                    </span>
                  </>
                )}
              </div>
            </div>
            <div className="absolute -bottom-12 -right-12 text-[200px] opacity-5">{getIcon(plan.icon)}</div>
          </div>
        </div>

        <div className={`p-6 relative z-10 flex-1 flex flex-col ${index === 1 && !isMobile ? 'pb-10' : ''}`}>
          <div className="bg-black/20 rounded-lg px-4 py-3 mb-4">
            <div className="flex items-center gap-2">
              {isUnlimitedPlan ? (
                <>
                  <FaInfinity className="text-green-400" />
                  <span className="text-green-400 font-light">{t('planCard.unlimited', 'UNLIMITED')}</span>
                  <span className="text-gray-400 text-sm font-light">{t('planCard.tokens', 'Tokens')}</span>
                </>
              ) : (
                <>
                  <FaRobot className="text-green-400" />
                  <span className="text-green-400 font-light">
                    {formatNumber(plan?.tokenAllocation)} {t('planCard.tokens', 'Tokens')}
                  </span>
                  <span className="text-gray-400 text-sm font-light">
                    {billingCycle === 'monthly'
                      ? t('planCard.perMonth', 'per month')
                      : t('planCard.perYear', 'per year')}
                  </span>
                </>
              )}
            </div>
          </div>

          {/* Cost per 1M tokens OR Dedicated CTO for Visionary Pro */}
          <div className="mb-6">
            {isUnlimitedPlan ? (
              <div className="text-center py-2 px-4 bg-blue-500 rounded-lg border border-blue-400/30">
                <span className="text-white text-sm font-light">
                  {t('planCard.dedicatedCto', 'Dedicated CTO - 24 Hours')}
                </span>
              </div>
            ) : (
              <div className="text-center py-2 px-4 bg-black/30 rounded-lg border border-gray-600/30">
                <span className="text-gray-300 text-sm font-light">
                  {t('planCard.perMillionTokens', { defaultValue: '$ {{price}} / 1M tokens', price: pricePerMillion })}
                </span>
              </div>
            )}
          </div>
          {getPlanDescription(plan?.description!) ? (
            <div className="flex-1 mb-6">
              <div className="bg-black/30 rounded-lg px-4 py-3 text-gray-200 font-light text-sm leading-relaxed">
                {getPlanDescription(plan?.description!)}
              </div>
            </div>
          ) : (
            <div className={'flex-1 mb-6'}></div>
          )}

          <div className="flex flex-col">
            <button
              onClick={() => !plan.currentPlan && onSubscribe(plan.stripeProductId, plan.stripePriceId)}
              className={`w-full py-3 rounded-lg transition-colors font-light ${
                plan.currentPlan ? 'bg-gray-700 text-gray-400 cursor-not-allowed' : 'bg-green-500 hover:bg-green-600'
              } ${index === 1 ? 'py-4 text-lg' : ''}`}
            >
              {plan.currentPlan ? t('planCard.currentPlan', 'Current Plan') : t('planCard.upgradePlan', 'Upgrade Plan')}
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};
