import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaTag, FaTimes } from 'react-icons/fa';
import { motion, AnimatePresence } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { formatNumber, ValueType } from '~/backOffice/components/billings/components/helper';
import { checkCouponCode } from '~/lib/stores/billing';

export interface Coupon {
  code: string;
  value: number;
  valueType: ValueType;
  isValid: boolean;
}

interface CouponInputProps {
  onApplyCoupon: (coupon: Coupon | null) => void;
  currentCoupon: Coupon | null;
}

const CouponInput: React.FC<CouponInputProps> = ({ onApplyCoupon, currentCoupon }) => {
  const [couponCode, setCouponCode] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [success, setSuccess] = useState<boolean>(false);
  const [showCouponInput, setShowCouponInput] = useState<boolean>(false);
  const { t } = useTranslation('profile');

  useEffect(() => {
    if (currentCoupon) {
      setCouponCode(currentCoupon.code);
      setSuccess(true);
      setError('');
    } else {
      setSuccess(false);
    }
  }, [currentCoupon]);

  const handleApplyCoupon = async () => {
    try {
      setLoading(true);
      const res = await checkCouponCode(couponCode);

      if (res.isValid) {
        const coupon: Coupon = {
          code: couponCode,
          isValid: res.isValid,
          value: res.value,
          valueType: res.valueType,
        };
        setSuccess(true);
        onApplyCoupon(coupon);
      } else {
        setError(res.message);
      }
    } catch (error) {
      setError(t('errorValidatingCoupon', 'Error validating coupon'));
      console.error('Error validating coupon:', error);
    } finally {
      setLoading(false);
    }
  };
  const handleRemoveCoupon = () => {
    setCouponCode('');
    setSuccess(false);
    setError('');
    onApplyCoupon(null);
  };

  const renderCouponValue = (coupon: Coupon) => {
    switch (coupon.valueType) {
      case ValueType.TOKENS:
        return `${formatNumber(coupon.value.toString())} Tokens`;
      case ValueType.PERCENTAGE_EXTRA_TOKENS:
        return `${coupon.value}% More Tokens`;
      case ValueType.PERCENTAGE_TOKEN_DISCOUNT:
        return `${coupon.value}% Discount`;
      default:
        return `${coupon.value}`;
    }
  };

  return (
    <div className="space-y-2 w-full mt-0!">
      {/* Toggle Switch Header - Centered */}
      <div className="flex items-center justify-center gap-4 mb-3">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-green-500/10 ring-1 ring-green-500/20">
            <FaTag className="text-green-400 text-lg" />
          </div>
          <span className={`text-base font-light ${currentCoupon ? 'text-gray-500' : 'text-gray-400'}`}>
            {t('haveCoupon', 'Have a coupon?')}
          </span>
        </div>
        <div
          className={`relative w-12 h-6 rounded-full cursor-pointer transition-colors duration-200 border ${
            currentCoupon ? 'bg-gray-600 border-gray-600 cursor-not-allowed' : 'bg-slate-700 border-slate-600'
          }`}
          onClick={() => !currentCoupon && setShowCouponInput(!showCouponInput)}
        >
          <motion.div
            className={`absolute top-[1px] w-5 h-5 rounded-full shadow-lg ${
              currentCoupon ? 'bg-gray-500' : 'bg-green-500'
            }`}
            animate={{
              left: showCouponInput ? '24px' : '2px',
            }}
            transition={{ type: 'spring', stiffness: 500, damping: 30 }}
          />
        </div>
      </div>

      {/* Animated Coupon Input Section */}
      <AnimatePresence>
        {showCouponInput && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            {!success ? (
              <div className="relative">
                <input
                  type="text"
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                  className={`w-full bg-slate-700/50 rounded-xl px-4 py-4 pr-32 text-white placeholder-gray-400 focus:outline-none focus:ring-2 transition-all ease-in-out ${
                    error
                      ? 'border border-red-500/50 focus:border-red-400 focus:ring-red-400/20'
                      : 'border border-green-500/50 focus:border-green-400 focus:ring-green-400/20'
                  }`}
                  placeholder={t('enterCouponCode', 'Enter coupon code...')}
                />
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <button
                    onClick={handleApplyCoupon}
                    disabled={loading || !couponCode.trim()}
                    className={`px-4 py-2 rounded-lg font-medium transition-all ${
                      loading || !couponCode.trim()
                        ? 'bg-slate-600 text-gray-400 cursor-not-allowed'
                        : 'bg-green-500 hover:bg-green-600 text-white'
                    }`}
                  >
                    {loading ? <FaSpinner className="animate-spin" /> : t('apply', 'Apply')}
                  </button>
                </div>
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0, y: 0 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-green-500/10 ring-1 ring-green-500/30 rounded-xl p-4"
              >
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-full bg-green-500/20">
                      <FaCheck className="text-green-400" />
                    </div>
                    <div>
                      <h4 className="text-green-400 font-medium">{currentCoupon?.code}</h4>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-green-400 font-medium">{currentCoupon && renderCouponValue(currentCoupon)}</span>
                    <button
                      onClick={handleRemoveCoupon}
                      className="p-2 rounded-full bg-gray-700/30 hover:bg-red-500/20 text-gray-400 hover:text-red-400 transition-colors"
                    >
                      <FaTimes />
                    </button>
                  </div>
                </div>
              </motion.div>
            )}

            {error && (
              <motion.p initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="text-red-400 text-sm mt-2">
                {error}
              </motion.p>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default CouponInput;
