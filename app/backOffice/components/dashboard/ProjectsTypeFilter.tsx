import React, { useEffect, useRef, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';

import {
  PlusIcon,
  FolderIcon,
  PencilSquareIcon,
  TrashIcon,
  Squares2X2Icon,
  CheckIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import {
  FaTimes,
  FaExclamationTriangle,
  FaCheck,
  FaFolder,
  FaTrash,
  FaSpinner,
} from 'react-icons/fa';
import { useTranslation } from 'react-i18next';
import {
  createFolder,
  deleteFolder,
  fetchProjects,
  getFolders,
  updateFolderName,
} from '~/api/projectsApi';
import CustomToast from "~/ai/components/CustomToast";

function ProjectsTypeFilter({
    projects,
    onFilterChange,
    showNewFolderInput,
    setShowNewFolderInput,
    selectedFolder,
    refreshProjects,
  }) {
  const { t } = useTranslation('translation');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [userCreatedFolders, setUserCreatedFolders] = useState([]);
  const [editingFolder, setEditingFolder] = useState(null);
  const [editedFolderName, setEditedFolderName] = useState('');
  const [newFolderName, setNewFolderName] = useState('');
  const [hoveredFolder, setHoveredFolder] = useState(null);
  const [allProjectsCount, setAllProjectsCount] = useState(0);
  const nameInputRef = useRef(null);
  const [deleteSuccess, setDeleteSuccess] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const [deleteConfirmationModal, setDeleteConfirmationModal] = useState<{
    open: boolean;
    folder: string | null;
    id: string;
  }>({
    open: false,
    folder: null,
    id: '',
  });

  const handleRequestDeleteFolder = (folderName: string, id: string) => {
    setDeleteConfirmationModal({ open: true, folder: folderName, id });
    setDeleteSuccess(false);
  };

  const handleConfirmDeleteFolder = async () => {
    if (deleteConfirmationModal.folder) {
      setIsDeleting(true);
      try {
        await deleteFolder(deleteConfirmationModal.id);
        await fetchFolders();
        await fetchAllProjectsCount();
        await refreshProjects();
        setDeleteSuccess(true);
        setTimeout(() => {
          setDeleteConfirmationModal({ open: false, folder: null, id: '' });
          setSelectedType('all');
          setDeleteSuccess(false);
        }, 1000);
        // setDeleteConfirmationModal({ open: false, folder: null, id: '' });
        // setSelectedType("all");
        // selectedFolder("all");
      } catch (error) {
        console.error('Error deleting folder:', error);
        setDeleteSuccess(false);
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const handleCancelDeleteFolder = () => {
    setDeleteConfirmationModal({ open: false, folder: null, id: '' });
    setDeleteSuccess(false);
  };

  const fetchFolders = async () => {
    try {
      const res = await getFolders();
      setUserCreatedFolders(res);
    } catch (error) {
      console.error('Error fetching folders:', error);
    }
  };

  const fetchAllProjectsCount = async () => {
    try {
      const data = await fetchProjects(undefined, 0, 1);
      setAllProjectsCount(data.totalItems);
    } catch (error) {
      console.error('Error fetching all projects count:', error);
    }
  };

  useEffect(() => {
    Promise.all([fetchFolders(), fetchAllProjectsCount()]);
  }, []);

  const handleFilterChange = (id: string) => {
    if (editingFolder) return;
    setSelectedType(id);
    onFilterChange(id);
  };

  const handleEditClick = (folderId, name) => {
    setEditingFolder(folderId);
    setEditedFolderName(name);
    setTimeout(() => {
      if (nameInputRef.current) {
        nameInputRef.current.focus();
        nameInputRef.current.select();
      }
    }, 0);
  };

  const handleNameSubmit = async (folderId: string) => {
    try {
      await updateFolderName(folderId, editedFolderName);
      await fetchFolders();
      setEditingFolder(null);

      showToast(
        'success',
        'Folder Updated',
        `“${editedFolderName}” has been renamed successfully.`
      );
    } catch (err) {
      console.error(err);
      showToast('error', 'Update Failed', 'Could not rename the folder.');
    }
  };


  const handleCreateNewFolder = async () => {
    const name = newFolderName.trim();
    if (!name) return;

    if (userCreatedFolders.some(f => f.name.toLowerCase() === name.toLowerCase())) {
      console.warn("Folder name already exists.");
      return;
    }

    await createFolder(name);
    await fetchFolders();
    setNewFolderName('');
    setShowNewFolderInput(false);
  };

  const [toast, setToast] = useState<{
    isVisible: boolean;
    type: ToastType;
    title: string;
    message: string;
  }>({
    isVisible: false,
    type: 'success',
    title: '',
    message: '',
  });

  const showToast = (type: ToastType, title: string, message: string) => {
    setToast({ isVisible: true, type, title, message });
  };

  useEffect(() => {
    const handleRefreshFolders = () => {
      fetchFolders();
      fetchAllProjectsCount();
    };

    window.addEventListener("refreshFolders", handleRefreshFolders);

    return () => {
      window.removeEventListener("refreshFolders", handleRefreshFolders);
    };
  }, []);


  return (
<>
    <div className="lg:w-full xl:w-[350px] bg-[#19263d] backdrop-blur-sm border border-white/10 rounded-xl p-6 flex flex-col">
      <div className="mb-4">
        <h2 className="text-2xl font-light text-white">{t('folders', 'Folders')}</h2>
        <p className="text-sm text-white/70 font-light">
          {t('organizeProjects', 'Organize your projects by category')}
        </p>
      </div>
      <button
        onClick={() => setShowNewFolderInput(true)}
        className="w-full flex items-center gap-2 px-4 py-2.5 rounded-lg transition-colors duration-200 bg-[#22D3EE]/10 hover:bg-[#22D3EE]/20 border border-[#22D3EE]/30 text-[#22D3EE] hover:text-[#22D3EE]/80 text-sm font-medium mb-4"
      >
        <PlusIcon className="w-4 h-4" />
        {t('createNewFolder', 'Create New Folder')}
      </button>
      {showNewFolderInput && (
        <div className="space-y-2 mb-4">
          <input
            type="text"
            value={newFolderName}
            onChange={(e) => setNewFolderName(e.target.value)}
            placeholder="Enter folder name"
            className="w-full bg-black/20 rounded-lg px-3 py-2 text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#22D3EE] font-light text-sm"
          />
          <div className="w-full flex gap-2">
            <button
              onClick={() => setShowNewFolderInput(false)}
              className="w-full px-3 py-1.5 rounded-lg bg-white/10 hover:bg-white/20 text-sm text-white"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateNewFolder}
              className="w-full px-3 py-1.5 rounded-lg bg-[#22D3EE] hover:bg-[#22D3EE]/80 text-sm text-black"
            >
              Save
            </button>
          </div>
        </div>
      )}
      <div className="flex-1 space-y-2 overflow-y-auto">
        <div
          key="all"
          onClick={() => handleFilterChange('all')}
          className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors text-left cursor-pointer hover:bg-white/5 text-white/90 ${
            selectedType === 'all' ? 'text-[#22D3EE]' : ''
          }`}
        >
          <div className="flex items-center gap-3 flex-1 min-w-0">
            <Squares2X2Icon className={`w-5 h-5 flex-shrink-0 ${selectedType === 'all' ? 'text-[#22D3EE]' : 'text-white/70'}`} />
            <span className="text-base font-light">{t('all', 'All')}</span>
          </div>
          <span className={`flex items-center gap-1.5 text-sm px-3 py-1.5 rounded-lg font-medium border ${
            selectedType === 'all'
              ? 'bg-[#22D3EE]/10 text-[#22D3EE] border-[#22D3EE]/20'
              : 'bg-white/10 text-white/70 border-white/10'
          }`}>
            <FolderIcon className="w-3 h-3" /> {allProjectsCount}
          </span>
        </div>

        {userCreatedFolders.map((folder) => {
          const isSelected = selectedType === folder._id;
          const isEditing = editingFolder === folder._id;
          return (
            <div
              key={folder._id}
              className="relative group"
              onMouseEnter={() => setHoveredFolder(folder._id)}
              onMouseLeave={() => setHoveredFolder(null)}
            >
              <div
                onClick={() => handleFilterChange(folder._id)}
                className={`gap-2 w-full flex items-center h-[65px] justify-between p-3 rounded-lg transition-colors text-left cursor-pointer ${
                  isSelected ? 'text-[#22D3EE]' : 'hover:bg-white/5 text-white/90'
                }`}
              >
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  <FolderIcon className={`w-5 h-5 flex-shrink-0 ${isSelected ? 'text-[#22D3EE]' : 'text-white/70'}`} />

                  {isEditing ? (
                    <div className="flex items-center gap-2 flex-1">
                      <input
                        ref={nameInputRef}
                        type="text"
                        value={editedFolderName}
                        onChange={(e) => setEditedFolderName(e.target.value)}
                        onClick={(e) => e.stopPropagation()}
                        className="text-base font-light bg-transparent focus:outline-none min-w-0 flex-1 text-white"
                        style={{maxWidth: '160px'}}
                      />
                    </div>
                  ) : (
                    <div className="overflow-hidden flex-1 min-w-0">
                      <span className="text-base font-light block whitespace-nowrap truncate text-base-input">
                        {folder.name}
                      </span>
                    </div>
                  )}
                </div>

                {isEditing ? (
                  <div className="flex gap-1 items-center bg-[#141e30] border border-white/10 rounded-lg p-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleNameSubmit(folder._id);
                      }}
                      className="p-1.5 rounded-md transition-colors duration-200 bg-black/20 border border-white/10 hover:border-[#334155]"
                    >
                      <CheckIcon className="w-4 h-4 text-green-400" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setEditingFolder(null);
                      }}
                      className="p-1.5 rounded-md transition-colors duration-200 bg-black/20 border border-white/10 hover:border-[#334155] bg-transparent"
                    >
                      <XMarkIcon className="w-4 h-4 text-red-400" />
                    </button>
                  </div>
                ) : (
                  <span className={`flex items-center gap-1.5 text-sm px-3 py-1.5 rounded-lg font-medium border ${
                    isSelected
                      ? 'bg-[#22D3EE]/10 text-[#22D3EE] border-[#22D3EE]/20'
                      : 'bg-white/10 text-white/70 border-white/10'
                  }`}>
                    <FolderIcon className="w-3 h-3" /> {folder.projectIds.length}
                  </span>
                )}
              </div>

              {hoveredFolder === folder._id && !isEditing && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-1 bg-[#141e30] border border-white/10 rounded-lg p-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleEditClick(folder._id, folder.name);
                    }}
                    className="p-1.5 rounded-md transition-colors duration-200 bg-black/5 border border-white/10 hover:border-[#334155]"
                  >
                    <PencilSquareIcon className="w-4 h-4 text-[#22D3EE]" />
                  </button>
                  <button
                    onClick={e => {
                      e.stopPropagation();
                      handleRequestDeleteFolder(folder.name, folder._id);
                    }}
                    className="p-1.5 rounded-md transition-colors duration-200 bg-black/5 border border-white/10 hover:border-[#334155]"
                  >
                    <TrashIcon className="w-4 h-4 text-red-400" />
                  </button>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
    <AnimatePresence>
    {deleteConfirmationModal.open && (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={handleCancelDeleteFolder}
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-[#0F1526] border border-white/10 rounded-2xl max-w-md w-full"
          onClick={e => e.stopPropagation()}
        >
          <div className="p-6 border-b border-white/10 flex justify-between items-center">
            <h3 className="text-xl font-medium text-white">
              {t('confirmDeleteFolder', 'Confirm Delete Folder')}
            </h3>
            <button onClick={handleCancelDeleteFolder} className="p-2 hover:bg-white/5 rounded-full bg-transparent">
              <XMarkIcon className="w-5 h-5 text-white/70" />
            </button>
          </div>

          <div className="p-6">
            <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4 mb-4">
              <div className="flex items-start gap-3">
                <div className="p-2 rounded-lg bg-red/8 flex items-center justify-center flex-shrink-0">
                  <FaExclamationTriangle className="text-red-400 w-4 h-4" />
                </div>
                <p className="text-white/90 text-sm font-medium">
                  {t('deleteFolderWarning', { folderName: deleteConfirmationModal.folder })}
                </p>
              </div>
            </div>

            {deleteSuccess ? (
              <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <FaCheck className="text-green-400" />
                  <p className="text-gray-300">
                    {t('folderDeletedSuccessfully', 'Folder deleted successfully')}
                  </p>
                </div>
              </div>
            ) : (
              <div className="bg-[#22D3EE]/6 border border-[#22D3EE]/20 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-[#22D3EE]/8 flex items-center justify-center flex-shrink-0">
                    <FaFolder className="w-4 h-4 text-gray-400 text-xl" />
                  </div>
                  <div>
                    <div className="text-white/90 text-sm font-medium">{deleteConfirmationModal.folder}</div>
                    <div className="text-white/60 text-sm font-light">
                      {userCreatedFolders.find(f => f._id === deleteConfirmationModal.id)?.projectIds.length || 0} projects
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="p-6 border-t border-white/10 flex justify-end gap-3">
            <button
              onClick={handleCancelDeleteFolder}
              className="px-6 py-2.5 bg-white/5 hover:bg-white/10 rounded-lg text-white/80 transition-colors"
              disabled={isDeleting}
            >
              {t('cancel', 'Cancel')}
            </button>
            <button
              onClick={handleConfirmDeleteFolder}
              className="px-4 py-2 bg-red-500/70 hover:bg-red-700 rounded-lg text-white flex items-center gap-2"
              disabled={isDeleting || deleteSuccess}
            >
              {isDeleting ? <FaSpinner className="animate-spin" /> : <FaTrash />}
              <span>{t('confirm', 'Confirm')}</span>
            </button>
          </div>
        </motion.div>
      </motion.div>
    )}
  </AnimatePresence>
    <div className="fixed top-4 right-4 z-50 pointer-events-none">
      <AnimatePresence>
        {toast.isVisible && (
          <CustomToast
            type={toast.type}
            title={toast.title}
            message={toast.message}
            isVisible={toast.isVisible}
            onClose={() => setToast((t) => ({ ...t, isVisible: false }))}
            duration={4000}
          />
        )}
      </AnimatePresence>
    </div>
</>
  );
}

export default ProjectsTypeFilter;
