import React, { useState, useRef, useEffect } from 'react';
import { FaChevronDown } from 'react-icons/fa';

type Folder = {
  _id: string;
  name: string;
};

type Props = {
  folders: Folder[];
  selectedFolderId: string | null;
  onChange: (id: string) => void;
};

const CustomDropdown: React.FC<Props> = ({ folders, selectedFolderId, onChange }) => {
  const [open, setOpen] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        setOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const selected = folders.find((f) => f._id === selectedFolderId);

  return (
    <div className="relative" ref={ref}>
      <button
        className="w-full px-4 py-2 bg-[#22D3EE]/10 text-white rounded-lg border border-white/10 flex justify-between items-center"
        onClick={() => setOpen(!open)}
      >
        {selected?.name || 'Select a folder'}
        <FaChevronDown className="ml-2 text-white/60 w-3 h-3" />
      </button>
      {open && (
        <div className="absolute z-50 mt-2 w-full bg-[#0F1526] rounded-lg shadow border border-white/10 max-h-60 overflow-y-auto">
          {folders.map((f) => (
            <div
              key={f._id}
              onClick={() => {
                onChange(f._id);
                setOpen(false);
              }}
              className={`px-4 py-2 text-sm cursor-pointer hover:bg-[#22D3EE]/10 ${
                f._id === selectedFolderId ? 'bg-[#22D3EE]/10 font-medium' : ''
              }`}
            >
              {f.name}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default CustomDropdown;
