import React, { useCallback, useEffect, useRef, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import { db, deleteById, updateChatDescription, useChatHistory } from '~/ai/lib/persistence';
import IconButton from '~/backOffice/components/dashboard/IconButton';
import { useLayoutEffect } from 'react';



import {
  XMarkIcon,
  EyeIcon,
  PencilSquareIcon,
  ArrowTopRightOnSquareIcon,
  ArrowDownTrayIcon,
  ChatBubbleBottomCenterTextIcon,
  DocumentDuplicateIcon,
  TrashIcon,
  ShareIcon,
  FolderArrowDownIcon,
} from '@heroicons/react/24/outline';

import {
  FolderSymlink,
  Plus,
  Sparkles,
  Search,
  X,
} from 'lucide-react';
import {
  Fa<PERSON><PERSON><PERSON>,
  FaExclamation<PERSON>riangle,
  FaChevron<PERSON>own,
  <PERSON>a<PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON><PERSON><PERSON>,
  FaTrash,
  FaRocket,
} from 'react-icons/fa';
import {
  changeProjectFolder,
  deleteProject,
  deleteProjectFolder,
  fetchContestProjects,
  fetchProjects,
  getFolders,
  postProjectToContest,
  updateContestProject,
  updateProjectName,
} from '~/api/projectsApi';
import { useNavigate } from 'react-router-dom';
import ProjectMetrics from './ProjectMetrics';
import debounce from 'lodash/debounce';
import SubmissionModal from './SubmissionModal';
import TransferModal from '~/backOffice/components/project/TransferProjectModal';
import ConfirmationDialog, { DialogConfirmationType } from '~/ai/components/ConfirmationDialog';
import { backendApiFetch } from '~/ai/lib/backend-api';
import { canDownloadOrExportAsync, canTransferProject } from '~/ai/lib/projectLimits';
import CustomDropdown from "~/backOffice/components/dashboard/CustomDropdown";

type FolderType = {
  userId: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  projectIds: string[];
  _id: string;
  __v: number;
};

type ProjectsListProps = {
  projects: Project[];
  onClose: () => void;
  selectedType: string;
  nextPage: (number: number) => void;
  selectedFolder: string;
  loading: boolean;
  refreshProjects: () => void;
  filterLoading: boolean;
  publishedProjects: string[];
};

function ProjectsListClient({
  projects,
  selectedFolder,
  selectedType,
  nextPage,
  setTotalPagesNumber,
  loading,
  refreshProjects,
  filterLoading,
  publishedProjects,
  updatePublishedProjects,
  triggerRefreshContestProjects,
  onClose,
}: ProjectsListProps & { triggerRefreshContestProjects: () => void }) {
  const { t } = useTranslation('translation');
  const [currentPage, setCurrentPage] = useState(1);
  const [initialPage, setInitialPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchedProjects, setSearchedProjects] = useState<Project[] | null>(null);
  const [searchLoading, setSearchLoading] = useState(false);
  const [showChangeFolderModal, setShowChangeFolderModal] = useState(false);
  const [selectedProjectId, setSelectedProjectId] = useState<string | null>(null);
  const [targetFolderId, setTargetFolderId] = useState<string | null>(null);
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);
  const [folders, setFolders] = useState<FolderType[]>([]);
  const { downloadProject, exportChat, duplicateCurrentChat } = useChatHistory();
  const [deletingProject, setDeletingProject] = useState<string | null>(null);
  const [loadingProjectId, setLoadingProjectId] = useState<string | null>(null);
  const [changingFolderProcess, setChangingFolderProcess] = useState(false);
  const [editingProjectId, setEditingProjectId] = useState<string | null>(null);
  const [editedProjectName, setEditedProjectName] = useState<string>('');
  const [originalProjectName, setOriginalProjectName] = useState<string>('');
  const [savingProjectName, setSavingProjectName] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDuplicateOpen, setIsDuplicateOpen] = useState(false);
  const [isDuplicating, setIsDuplicating] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<{ id: string; slug: string } | null>(null);
  const [projectToDuplicate, setProjectToDuplicate] = useState<{ id: string; slug: string; name: string } | null>(null);
  const [deletingInProgress, setDeletingInProgress] = useState(false);
  const [deleteSuccess, setDeleteSuccess] = useState(false);
  const [exportChatId, setExportChatId] = useState<string | null>(null);
  const [duplicatingProjectId, setDuplicatingProjectId] = useState<string | null>(null);
  const [downloadingProjectId, setDownloadingProjectId] = useState<string | null>(null);
  const navigate = useNavigate();
  const [hiddenCodeIcons, setHiddenCodeIcons] = useState<number[]>([]);
  const iconProps = { size: 18, strokeWidth: 1.5 };
  const [contestProjects, setContestProjects] = useState<ContestProject[]>([]);
  const [modalType, setModalType] = useState<'publish' | 'refresh'>('publish');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalState, setModalState] = useState<'loading' | 'success' | 'error'>('loading');
  const [currentProjectSlug, setCurrentProjectSlug] = useState<string | null>(null);
  const projectsContainerRef = useRef<HTMLDivElement>(null);
  const [showTransferProjectModal, setShowTransferProjectModal] = useState<string | boolean>(false);
  const [loadingTransferProjects, setLoadingTransferProjects] = useState<boolean>(false);
  const [transferError, setTransferError] = useState<string | null>(null);
  const { loadChatById, loadChat } = useChatHistory();
  const [hoveredEyeProjectSlug, setHoveredEyeProjectSlug] = useState<string | null>(null);
  const hoveredProject = projects.find(p => p.projectSlug === hoveredEyeProjectSlug) || null;
  const popupRef = useRef<HTMLDivElement>(null);
  const [popupSize, setPopupSize] = useState({ width: 0, height: 0 });
  const [internalProjects, setInternalProjects] = useState<Project[]>(projects);

  useEffect(() => {
    setInternalProjects(projects);
  }, [projects]);

  useEffect(() => {
    const fetchContestData = async () => {
      try {
        const data = await fetchContestProjects();
        setContestProjects(data.projects || []);
      } catch (error) {
        console.error('Error fetching contest projects:', error);
      }
    };

    fetchContestData();
  }, []);

  const getContestProjectId = (projectId: string): string | undefined => {
    // Verificăm dacă proiectul există în displayedProjects
    const projectExists = displayedProjects.some((project) => project._id === projectId);

    if (!projectExists) {
      // Dacă nu există, nu se poate avea un contestProject asociat
      return undefined;
    }

    // Căutăm în contestProjects un obiect care are projectId-ul egal cu cel primit ca parametru
    const contestProject = contestProjects.find((contestProj) => contestProj.projectId === projectId);

    // Dacă am găsit, returnăm id-ul din contestProject; altfel, returnăm undefined
    return contestProject ? contestProject.id : undefined;
  };

  const handlePublishToContest = async (projectSlug: string) => {
    setCurrentProjectSlug(projectSlug);
    setModalType('publish');
    setModalState('loading');
    setIsModalOpen(true);

    try {
      const response = await submitProjectContest(projectSlug);

      if (response && response.id) {
        setModalState('success');

        if (!publishedProjects.includes(projectSlug)) {
          publishedProjects.push(projectSlug);
        }

        updatePublishedProjects(projectSlug, true);
        triggerRefreshContestProjects();
        refreshProjects();
      } else {
        console.error('Unexpected response format:', response);
        setModalState('error');
      }
    } catch (error: any) {
      console.error('Error publishing project to contest:', error);
      setModalState('error');
    }
  };

  const handleRefreshContestSubmission = async (projectSlug: string) => {
    setCurrentProjectSlug(projectSlug);
    setModalType('refresh');
    setModalState('loading');
    setIsModalOpen(true);

    try {
      await updateContestProject(projectSlug);

      const updatedContestProjects = await fetchContestProjects();
      setContestProjects(updatedContestProjects.projects || []);

      setModalState('success');
    } catch (error: any) {
      console.error('Error refreshing contest submission:', error);
      setModalState('error');
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setCurrentProjectSlug(null);
    setModalState('loading');
  };

  const handleViewContest = () => {
    window.open('https://biela.dev/hackathon', '_blank');
    handleCloseModal();
  };

  const isProjectPublished = (projectSlug: string) => {
    return publishedProjects.includes(projectSlug);
  };

  const isProjectPublishedToContest = (projectSlug: string) => {
    return contestProjects.some((contestProject) => contestProject.projectSlug === projectSlug);
  };

  const getProjectLikes = (projectSlug: string) => {
    const contestProject = contestProjects.find((contestProject) => contestProject.projectSlug === projectSlug);
    return contestProject ? contestProject.likesCount : 0;
  };

  const fetchFolders = async () => {
    try {
      const res = await getFolders();
      setFolders(res);
    } catch (error) {
      console.error('Error fetching folders:', error);
    }
  };

  useEffect(() => {
    fetchFolders();
  }, []);

  const submitProjectContest = async (projectSlug: string) => {
    try {
      const response = await postProjectToContest(projectSlug);

      return response;
    } catch (error) {
      console.error('Error posting project:', error);
      throw error;
    }
  };

  const enhancedRefreshProjects = async () => {
    try {
      await refreshProjects();

      if (displayedProjects.length === 0 && currentPage > 1) {
        handlePageChange(currentPage - 1);
      }
    } catch (error) {
      console.error('Error refreshing projects:', error);
    }
  };

  const handleOpenProject = async (projectSlug: string) => {
    setLoadingProjectId(projectSlug);

    navigate('/chat/' + projectSlug);
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);

    if (searchTerm.trim() !== '') {
      handleSearch(searchTerm, newPage);
    } else {
      nextPage(newPage - 1);
    }

    setTimeout(() => {
      if (projectsContainerRef.current) {
        const element = projectsContainerRef.current;
        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.scrollY - 180;

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth',
        });
      }
    }, 100);
  };

  const handleSearch = useCallback(
    debounce(async (term: string, page: number) => {
      setSearchLoading(true);

      if (term.trim() === '') {
        setSearchedProjects(null);
        setCurrentPage(1);
        nextPage(0);
        setSearchLoading(false);
        await enhancedRefreshProjects();

        return;
      }

      try {
        const folderId = selectedFolder === 'all' ? undefined : selectedFolder;
        const result = await fetchProjects(folderId, page - 1, 10, 'updatedAt', 'DESC', term);

        setSearchedProjects(result.items);
        setTotalPagesNumber(result.totalPages || 1);
        setCurrentPage(page);
      } catch (error) {
        console.error('Error searching projects:', error);
        setSearchedProjects([]);
      } finally {
        setSearchLoading(false);
      }
    }, 500),
    [selectedFolder, nextPage, setTotalPagesNumber]
  );

  const [prevSearchTerm, setPrevSearchTerm] = useState('');
  useEffect(() => {
    if (searchTerm !== prevSearchTerm) {
      if (prevSearchTerm === '' && searchTerm !== '') {
        setInitialPage(currentPage);
      }

      setPrevSearchTerm(searchTerm);
      handleSearch(searchTerm, searchTerm ? 1 : initialPage);
    }
  }, [searchTerm, handleSearch, prevSearchTerm, currentPage]);

  const displayedProjects = searchedProjects !== null
    ? searchedProjects
    : internalProjects;
  const paginatedProjects = displayedProjects;

  useEffect(() => {
    if (displayedProjects.length === 0 && currentPage > 1 && !loading && !filterLoading && !searchLoading) {
      handlePageChange(currentPage - 1);
    }
  }, [displayedProjects, currentPage, loading, filterLoading, searchLoading]);

  useEffect(() => {
    getFolders().then((res) => setFolders(res));
  }, [showChangeFolderModal]);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const projectToOpen = urlParams.get('open');
    if (projectToOpen) {
      handleOpenProject(projectToOpen);
    }
  }, []);

  const formatDate = (dateString: string) => {
    if (!dateString) {
      return 'Invalid date';
    }

    const date = new Date(dateString);

    if (isNaN(date.getTime())) {
      const parsedDate = dateString.split('T')[0];
      return parsedDate || 'Invalid date';
    }

    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: '2-digit',
    });
  };

  const handleOpenChangeFolderModal = (projectId: number | string) => {
    const projectIdStr = projectId.toString();
    setSelectedProjectId(projectIdStr);

    const currentFolder = folders.find((folder) => folder.projectIds.includes(projectIdStr));
    setCurrentFolderId(currentFolder ? currentFolder._id : null);
    setTargetFolderId(currentFolder ? currentFolder._id : null);
    setShowChangeFolderModal(true);
  };

  const handleConfirmChangeFolder = async () => {
    if (!targetFolderId || !selectedProjectId) {
      return;
    }

    setChangingFolderProcess(true);

    try {
      const currentFolder = folders.find((folder) => folder.projectIds.includes(selectedProjectId));

      if (currentFolder && currentFolder._id !== targetFolderId) {
        await deleteProjectFolder(selectedProjectId, currentFolder._id);
      }

      await changeProjectFolder(targetFolderId, selectedProjectId || '');
      setShowChangeFolderModal(false);
      setSelectedProjectId(null);
      setTargetFolderId(null);
      setCurrentFolderId(null);
      await enhancedRefreshProjects();
    } finally {
      setChangingFolderProcess(false);
    }
  };

  const handleExportChat = async (projectSlug: string) => {
    if (!(await canDownloadOrExportAsync(projectSlug, 'export'))) {
      if (window.showToast) {
        window.showToast({
          type: 'error',
          title: 'Error',
          message: 'You have reached the export limit for this project. Make changes to export again.',
          duration: 4000,
        });
      }
      //toast.error('You have reached the export limit for this project. Make changes to export again.');
      return;
    }

    executeWithMinLoadingTime(() => exportChat(projectSlug), setExportChatId, projectSlug, 'Error exporting chat:');

    if (window.showToast) {
      window.showToast({
        type: 'success',
        title: 'Success',
        message: 'Project chat successfully downloaded!',
        duration: 4000,
      });
    }
  };

  const handleDuplicateProject = async (projectSlug: string, projectName: string) => {
    setIsDuplicating(true);

    await executeWithMinLoadingTime(
      () => duplicateCurrentChat(projectSlug, projectName),
      setDuplicatingProjectId,
      projectSlug,
      'Error duplicating project:'
    );

    setIsDuplicating(false);
    if (window.showToast) {
      window.showToast({
        type: 'success',
        title: 'Success',
        message: 'Project duplicated!',
        duration: 4000,
      });
    }
    //toast.success('Project duplicated!');
  };

  const handleDownloadProject = async (projectSlug: string) => {
    if (!(await canDownloadOrExportAsync(projectSlug, 'download'))) {
      if (window.showToast) {
        window.showToast({
          type: 'error',
          title: 'Error',
          message: 'You have reached the download limit for this project. Make changes to download again.',
          duration: 4000,
        });
      }
      //toast.error('You have reached the download limit for this project. Make changes to download again.');
      return;
    }

    executeWithMinLoadingTime(
      () => downloadProject(projectSlug),
      setDownloadingProjectId,
      projectSlug,
      'Error downloading project:'
    );

    if (window.showToast) {
      window.showToast({
        type: 'success',
        title: 'Success',
        message: 'Project downloaded successfully!',
        duration: 4000,
      });
    }
  };

  const handleEditProjectName = (projectId: string, currentName: string) => {
    setEditingProjectId(projectId);
    setEditedProjectName(currentName);
    setOriginalProjectName(currentName);
  };

  const handleSaveProjectName = async (projectId: string) => {
    if (editedProjectName.trim() === '' || editedProjectName === originalProjectName) {
      setEditingProjectId(null);
      return;
    }

    setSavingProjectName(true);

    try {
      const project = internalProjects.find(p => p._id === projectId);
      if (!project) throw new Error('Project not found');

      await updateProjectName(project.projectSlug, editedProjectName);
      await updateChatDescription(db, project.projectSlug.split('-')[0], editedProjectName);

      setInternalProjects(prev =>
        prev.map(p =>
          p._id === projectId
            ? { ...p, projectName: editedProjectName }
            : p
        )
      );

      setEditingProjectId(null);
    } catch (error) {
      console.error('Error updating project name:', error);
    } finally {
      setSavingProjectName(false);
    }
  };

  const handleCancelEditProjectName = () => {
    setEditingProjectId(null);
    setEditedProjectName('');
    setOriginalProjectName('');
  };

  const handleOpenDeleteConfirm = (projectId: string, projectSlug: string) => {
    setProjectToDelete({ id: projectId, slug: projectSlug });
    setShowDeleteConfirm(true);
    setDeleteSuccess(false);
  };

  const handleConfirmDelete = async () => {
    if (!projectToDelete) {
      return;
    }

    setDeletingInProgress(true);
    setDeletingProject(projectToDelete.id);

    setTimeout(async () => {
      try {
        if (!selectedFolder || selectedFolder === 'all') {
          await deleteProject(projectToDelete.slug);
        } else {
          await deleteProjectFolder(projectToDelete.id, selectedFolder);
        }

        if (db) {
          await deleteById(db, projectToDelete.slug.split('-')[0].toString());
        }

        await enhancedRefreshProjects();

        setDeleteSuccess(true);
        setTimeout(() => {
          setShowDeleteConfirm(false);
          setDeletingProject(null);
          setProjectToDelete(null);
          setDeleteSuccess(false);
        }, 1000);
      } catch (error) {
        console.error('Error deleting project from folder:', error);
        setDeleteSuccess(false);
      } finally {
        setDeletingInProgress(false);
      }
    }, 300);
  };

  const handleCancelDelete = () => {
    setShowDeleteConfirm(false);
    setProjectToDelete(null);
    setDeleteSuccess(false);
  };

  const handleDuplicate = async (projectId: string, projectSlug: string, projectName: string) => {
    setIsDuplicateOpen(true);
    setProjectToDuplicate({ id: projectId, slug: projectSlug, name: projectName });
  };

  const executeWithMinLoadingTime = async (
    operation: () => Promise<any>,
    setLoadingState: (value: string | null) => void,
    projectSlug: string,
    errorMessage: string,
    minLoadingTime = 800
  ) => {
    const startTime = Date.now();

    setLoadingState(projectSlug);

    try {
      await operation();
    } catch (error) {
      console.error(errorMessage, error);
    } finally {
      const elapsedTime = Date.now() - startTime;
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime);

      if (remainingTime > 0) {
        await new Promise((resolve) => setTimeout(resolve, remainingTime));
      }

      setLoadingState(null);
    }
  };

  function getPagination(current: number, total: number) {
    const delta = 2;
    const range: (number | string)[] = [];
    const left = Math.max(2, current - delta);
    const right = Math.min(total - 1, current + delta);

    range.push(1);

    if (left > 2) {
      range.push('...');
    }

    for (let i = left; i <= right; i++) {
      range.push(i);
    }

    if (right < total - 1) {
      range.push('...');
    }

    if (total > 1) {
      range.push(total);
    }

    return range;
  }

  const currentFolder = folders.find((folder) => folder._id === selectedFolder)?.name || 'All';

  const handleTransferProject = async (transferTo: string) => {
    setLoadingTransferProjects(true);

    const slug = showTransferProjectModal as string;

    if (!slug) {
      setTransferError(t('projectActions.invalidSlug'));
      setLoadingTransferProjects(false);

      return;
    }

    if (!(await canTransferProject(slug, transferTo))) {
      setTransferError(
        t(
          'projectActions.transferLimit',
          'You have already transferred this version of the project to this user. Make changes to transfer again.'
        )
      );
      setLoadingTransferProjects(false);

      return;
    }

    try {
      const res = await backendApiFetch(`/user-projects/${showTransferProjectModal}/transfer/${transferTo}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });
      const data = await res.json();
      if (!res.ok) {
        throw new Error(data.message || 'transfer failed');
      }
      if (window.showToast) {
        window.showToast({
          type: 'success',
          title: 'Success',
          message: t('transferSuccess', { user: transferTo }),
          duration: 4000,
        });
      }
      //toast.success(t('transferSuccess', { user: transferTo }));
      setTransferError(null);
      handleCloseTransfer();
    } catch (err: any) {
      const message = (err.message || '').toLowerCase();
      let errorKey: string;
      let defaultText: string;

      if (message.includes('user') && message.includes('not found')) {
        errorKey = 'error.userNotFound';
        defaultText = 'User not found.';
      } else if (message.includes('project') && message.includes('not found')) {
        errorKey = 'error.projectNotFound';
        defaultText = 'Project not found.';
      } else if (message.includes('cannot transfer a project to your own account')) {
        errorKey = 'transferErrorOwnAccount';
        defaultText = 'You cannot transfer a project to your own account.';
      } else if (message.includes('permission')) {
        errorKey = 'dontHavePermissionToTransfer';
        defaultText = 'You do not have permission to transfer this project.';
      } else {
        errorKey = 'error.generic';
        defaultText = 'Something went wrong. Please try again.';
      }

      setTransferError(t(errorKey, defaultText));
    } finally {
      setLoadingTransferProjects(false);
    }
  };

  const handleCloseTransfer = () => {
    setShowTransferProjectModal(false);
    setTransferError(null);
  };

  useEffect(() => {
    if (!searchTerm.trim()) {
      setSearchedProjects(null);
      return;
    }

    const lowerSearch = searchTerm.toLowerCase();
    const filtered = projects.filter((proj) =>
      proj.projectName.toLowerCase().includes(lowerSearch)
    );
    setSearchedProjects(filtered);
  }, [searchTerm, projects]);

  const debouncedSearch = useCallback(
    debounce((val) => {
      setSearchTerm(val);
    }, 300),
    []
  );

  const [showPreview, setShowPreview] = useState(false);
  const [previewPosition, setPreviewPosition] = useState({ iconRight: 0 });
  const eyeIconRef = useRef<HTMLElement | null>(null);
  const hidePreviewTimeoutRef = useRef<number>();

  const gapFromIcon = 40;
  const desiredPopupWidth = 600;

  const handleMouseEnterPreviewTrigger = (slug: string) => {
    if (hidePreviewTimeoutRef.current) clearTimeout(hidePreviewTimeoutRef.current);
    setHoveredEyeProjectSlug(slug);
    setShowPreview(true);
    if (eyeIconRef.current) {
      const rect = eyeIconRef.current.getBoundingClientRect();
      setPreviewPosition({ iconRight: rect.right });
    }
  };

  const handleMouseLeavePreviewArea = () => {
    hidePreviewTimeoutRef.current = window.setTimeout(() => {
      setShowPreview(false);
      setHoveredEyeProjectSlug(null);
    }, 200);
  };

  const handleMouseEnterPopup = () => {
    if (hidePreviewTimeoutRef.current) {
      clearTimeout(hidePreviewTimeoutRef.current);
    }
  };

  const handleClosePreviewPopup = (e) => {
    e.stopPropagation();
    if (hidePreviewTimeoutRef.current) {
      clearTimeout(hidePreviewTimeoutRef.current);
    }
    setShowPreview(false);
  };

  useLayoutEffect(() => {
    if (showPreview && popupRef.current) {
      const { width, height } = popupRef.current.getBoundingClientRect();
      setPopupSize({ width, height });
    }
  }, [showPreview]);

  const availableSpaceRight = window.innerWidth - previewPosition.iconRight - gapFromIcon;
  const width = Math.min(desiredPopupWidth, availableSpaceRight);
  const left = previewPosition.iconRight + gapFromIcon;


  return (
    <div className="flex-1 relative">
      <ReactTooltip id="icon-tooltip" place="top" effect="solid" className="z-[9999] project-action-tooltip" />
      <div className="bg-[#19263d] backdrop-blur-sm rounded-xl border border-white/10 shadow-xl p-4 lg:p-6 mb-6">
        <div className="lg:flex items-center justify-between gap-6">
          <div className="flex md:flex-row flex-col md:items-center items-start gap-3 lg:mb-0 mb-3">
            <div className="p-2 bg-[#22D3EE]/10 rounded-lg border border-[#22D3EE]/20">
              <FolderSymlink className="w-6 h-6 text-[#22D3EE]" strokeWidth={1} />
            </div>
            <div>
              <h2 className="text-2xl font-light text-white capitalize">
                {loading || filterLoading || searchLoading
                  ? t('pleaseWait', 'Please wait...')
                  : selectedType === 'all'
                    ? t('projectsInAll', 'All Projects')
                    : t('projectsInCurrentFolder', 'Projects in', { folderName: currentFolder })}
              </h2>
              <div className="flex items-center gap-4 mt-1">
                <p className="text-sm text-white/70 font-light">{t('viewProjects', 'Viewing all projects across folders')}</p>
                <span className="text-sm text-white/60">
                  {t('projectCount', { count: displayedProjects.length })}
                </span>
              </div>
            </div>
          </div>

          <div className="relative md:min-w-[320px]">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/50" />
            <input
              type="text"
              placeholder={t('searchProjects', 'Search Project...')}
              className="w-full pl-10 pr-4 py-3 bg-[#161E2D] border border-[#22D3EE]/30 bg-[#22D3EE]/10 text-[#22D3EE] rounded-lg placeholder-white/50 focus:outline-none focus:border-[#22D3EE]/50 transition-colors duration-200 outline-none"
              value={searchTerm}
              onChange={(e) => {
                const newValue = e.target.value;
                setSearchTerm(newValue);
                handleSearch(newValue, 1);
              }}
            />
          </div>
        </div>
      </div>

      {loading || filterLoading || searchLoading ? (
        <div className="flex items-center justify-center h-40">
          <FaSpinner className="animate-spin text-3xl text-green-400" />
        </div>
      ) : displayedProjects.length > 0 ? (
        <>
          <div ref={projectsContainerRef} className="space-y-4 lg:space-y-6">
            {paginatedProjects.map((project, index) => (
              <motion.div
                key={project._id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: deletingProject === project._id ? 0 : 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="bg-[#19263d] backdrop-blur-sm rounded-xl border border-white/10 shadow-xl overflow-hidden relative"
              >

                <div className="p-4 flex lg:flex-row flex-col items-center gap-3 justify-between border-b border-white/5 pt-12">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    {folders && (
                      <div className="absolute top-0 left-0 z-10">
                        <span className="flex items-center gap-1.5 text-xs px-3 py-1.5 rounded-br-lg font-medium border-r border-b border-white/10 bg-[#4ADE80]/10 text-[#4ADE80]">
                         <FolderArrowDownIcon className="w-4 h-4 text-green-400" />
                          {folders.find((folder) => folder.projectIds.includes(project._id))?.name || t('all', 'All')}
                        </span>
                      </div>
                    )}
                    <div className="flex bg-black/20 p-1.5 rounded-lg items-center gap-1.5 border border-white/10">
                      {project.isDeployed && (
                          <motion.a
                            title="Launch Project"
                            rel="noopener noreferrer"
                            href={
                              project.cloudAppUrl
                                ? project.cloudAppUrl.startsWith('http')
                                  ? project.cloudAppUrl
                                  : `https://${project.cloudAppUrl}`
                                : undefined
                            }
                            target="_blank"
                            tooltip="Live App"
                            data-tooltip-id="icon-tooltip"
                            data-tooltip-content="Live App"
                            className="project-action text-sm max-[415px]:flex-1 bg-black/20 border border-white/10 text-[#94A3B8] p-1.5 backdrop-blur-sm hover:text-[#F8FAFC] hover:border-[#334155] group text-white rounded-lg py-2.2 px-2.2 font-light shadow-md flex items-center justify-center gap-2 group relative overflow-hidden"
                          >
                            <FaRocket color={'#22d3ee'}/>
                          </motion.a>
                      )}
                      {project.screenshotUrl && (
                        <span
                          ref={eyeIconRef}
                          onMouseEnter={() => handleMouseEnterPreviewTrigger(project.projectSlug)}
                          onMouseLeave={handleMouseLeavePreviewArea}
                          className="inline-block"
                        >
                          <IconButton
                            icon={<EyeIcon className="w-5 h-5 text-cyan-400" />}
                            tooltip="Preview Project Screenshot"
                          />
                        </span>
                      )}
                      <IconButton
                        icon={<PencilSquareIcon className="w-5 h-5 text-[#22D3EE]" />}
                        onClick={() =>
                          handleEditProjectName(
                            project._id.toString(),
                            project.projectName || project.projectSlug
                          )
                        }
                        tooltip="Edit Project Name"
                        data-tooltip-id="icon-tooltip"
                        data-tooltip-content="Edit Project Name"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      {editingProjectId === project._id.toString() ? (
                        <input
                          type="text"
                          value={editedProjectName}
                          onChange={(e) => setEditedProjectName(e.target.value)}
                          onBlur={() => handleSaveProjectName(project._id.toString())}
                          onKeyDown={e => {
                            if (e.key === 'Enter') {
                              e.currentTarget.blur();
                            }
                          }}
                          autoFocus
                          className="text-lg font-semibold text-white bg-transparent border-b border-white/30 focus:border-white/60 focus:outline-none px-1 py-1 min-w-0 w-full"
                        />
                      ) : (
                        <div className={'flex flex-col'}>
                          <h3
                            className="text-lg font-normal text-white cursor-pointer hover:text-white/80 transition-colors truncate title-responsive-mob"
                            onClick={() =>
                              handleEditProjectName(
                                project._id.toString(),
                                project.projectName || project.projectSlug
                              )
                            }
                          >
                            {project.projectName || project.projectSlug}
                          </h3>
                          <p className="text-sm text-white/60 font-light truncate">{project.description}</p>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2 lg:gap-3">
                    <button
                      onClick={() =>
                        handleOpenProject(project.projectSlug.split('-')[0].toString())
                      }
                      disabled={loadingProjectId === project.projectSlug.split('-')[0]}
                      className="hidden lg:flex items-center gap-2 px-4 py-2.5 rounded-lg transition-colors duration-200 bg-green-500/10 hover:bg-green-500/20 border border-green-500/30 text-green-400 hover:text-green-300 text-sm font-medium"
                    >
                      {loadingProjectId === project.projectSlug.split('-')[0] ? (
                        <FaSpinner className="animate-spin w-5 h-5 text-white" />
                      ) : (
                        <>
                          <ArrowTopRightOnSquareIcon className="w-5 h-5" />
                          {t('openProject', 'Open Project')}
                        </>
                      )}
                    </button>
                    <button
                      onClick={() =>
                        handleOpenProject(project.projectSlug.split('-')[0].toString())
                      }
                      className="lg:hidden flex items-center gap-2 px-3 py-2 rounded-lg transition-colors duration-200 bg-green-500/10 hover:bg-green-500/20 border border-green-500/30 text-green-400 hover:text-green-300 text-sm font-medium"
                    >
                      <ArrowTopRightOnSquareIcon className="w-4 h-4" />
                      <span className="hidden sm:inline">Open</span>
                    </button>
                    <div className="flex bg-black/20 p-1.5 rounded-lg items-center gap-1.5 border border-white/10">
                      <IconButton
                        icon={<ArrowDownTrayIcon className="w-5 h-5 text-purple-400" />}
                        onClick={() => handleDownloadProject(project.projectSlug)}
                        tooltip="Download Project Files"
                        isLoading={downloadingProjectId === project.projectSlug}
                        data-tooltip-id="icon-tooltip"
                        data-tooltip-content="Download Project Files"
                      />
                      <IconButton
                        icon={
                          <ChatBubbleBottomCenterTextIcon className="w-5 h-5 text-cyan-400" />
                        }
                        onClick={() => handleExportChat(project.projectSlug)}
                        tooltip="Download Project Chat"
                        isLoading={exportChatId === project.projectSlug}
                        data-tooltip-id="icon-tooltip"
                        data-tooltip-content="Download Project Chat"
                      />
                      <IconButton
                        icon={<DocumentDuplicateIcon className="w-5 h-5 text-[#22D3EE]" />}
                        onClick={() =>
                          handleDuplicate(
                            project._id.toString(),
                            project.projectSlug,
                            project.projectName
                          )
                        }
                        tooltip="Duplicate Project"
                        isLoading={duplicatingProjectId === project.projectSlug}
                        data-tooltip-id="icon-tooltip"
                        data-tooltip-content="Duplicate Project"
                      />
                      <IconButton
                        icon={<TrashIcon className="w-5 h-5 text-red-400" />}
                        onClick={() =>
                          handleOpenDeleteConfirm(
                            project._id.toString(),
                            project.projectSlug
                          )
                        }
                        tooltip="Delete Project"
                        data-tooltip-id="icon-tooltip"
                        data-tooltip-content="Delete Project"
                      />
                    </div>
                    <div className="flex bg-black/20 p-1.5 rounded-lg items-center gap-1.5 border border-white/10">
                      <IconButton
                        icon={<ShareIcon className="w-5 h-5 text-purple-400" />}
                        onClick={() => setShowTransferProjectModal(project.projectSlug)}
                        tooltip="Share Project With a BIELA User"
                        data-tooltip-id="icon-tooltip"
                        data-tooltip-content="Share Project With a BIELA User"
                      />
                      <IconButton
                        icon={<FolderArrowDownIcon className="w-5 h-5 text-cyan-400" />}
                        onClick={() => handleOpenChangeFolderModal(project._id)}
                        tooltip="Change Folder"
                        data-tooltip-id="icon-tooltip"
                        data-tooltip-content="Change Folder"
                      />
                    </div>
                  </div>
                </div>

                {/* Project Metrics */}
                {project.estimations && project.totalTokensCost !== 0 && (
                    <ProjectMetrics
                      index={index}
                      hideIcon={(e) => {
                        if (e === undefined) {
                          // scoate indexul din listÄƒ
                          setHiddenCodeIcons((prev) => prev.filter((i) => i !== index));
                        } else {
                          // adaugÄƒ indexul Ã®n listÄƒ (dacÄƒ nu e deja)
                          setHiddenCodeIcons((prev) => [...new Set([...prev, index])]);
                        }
                      }}
                      metrics={{
                        timeMetrics: {
                          traditional: project.estimations.estimatedTimeTraditional,
                          actual: project.timeSpentAi,
                        },
                        estimatedCost: {
                          traditional: project.estimations.estimatedCostTraditional,
                          tokens: project.totalTokensCost,
                        },
                        estimations: {
                          confidenceScore: project.estimations.confidenceScore,
                          estimatedCostTraditional: project.estimations.estimatedCostTraditional,
                          estimatedTimeTraditional: project.estimations.estimatedTimeTraditional,
                          estimatedNumberOfDevelopers: project.estimations.estimatedNumberOfDevelopers,
                          recommendedDeveloperLevel: project.estimations.recommendedDeveloperLevel,
                          timeToMarket: project.estimations.timeToMarket,
                          maintenanceCostPercentage: project.estimations.maintenanceCostPercentage,
                          projectType: project.estimations.projectType,
                          projectComplexity: project.estimations.projectComplexity,
                          uniqueComponentCount: project.estimations.uniqueComponentCount,
                          featureCount: project.estimations.featureCount,
                          rangeOfUncertainty: project.estimations.rangeOfUncertainty,
                          keyTechnologies: project.estimations.keyTechnologies,
                          breakdown: project.estimations.breakdown,
                        },
                      }}
                      projectUpdatedAt={project.updatedAt}
                      projectCreatedAt={project.createdAt}
                      // projectDeployedAt={project.DeployedAt}
                      updatePublishedProjects={updatePublishedProjects}
                      refreshContestProjects={triggerRefreshContestProjects}
                      onPublish={handlePublishToContest}
                      publishingSlug={currentProjectSlug}
                      publishingState={modalState}
                    />
                )}
              </motion.div>
            ))}
          </div>
        </>
      ) : searchedProjects !== null ? (
        // **NEW**: you did run a search but got zero hits
        <div className="bg-navy-800 rounded-xl p-10 overflow-hidden relative">
          {/* Decorative elements for futuristic feel */}
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500/0 via-blue-500/50 to-purple-500/0"></div>
          <div className="absolute bottom-0 right-0 w-1/3 h-1 bg-gradient-to-r from-purple-500/0 via-purple-500/30 to-blue-500/0"></div>

          <div className="flex flex-col items-center justify-center py-8 relative">
            {/* Subtle grid background */}
            <div className="absolute inset-0 bg-[radial-gradient(#2a3a5a_1px,transparent_1px)] bg-[size:20px_20px] opacity-20"></div>

            <h2 className=" text-2xl font-light font-manrope mb-4 text-white text-center">
              {t('NoProjectFound', 'No project found with that name.')}
            </h2>

            <p className="text-center text-gray-400 max-w-xl mb-8 text-md font-light leading-normal">
              {t('NoProjectFoundDescription', 'Please check for any typos and try again.')}
            </p>
          </div>
        </div>
      ) : (
        <div className="bg-navy-800 rounded-xl p-10 overflow-hidden relative">
          {/* Decorative elements for futuristic feel */}
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500/0 via-blue-500/50 to-purple-500/0"></div>
          <div className="absolute bottom-0 right-0 w-1/3 h-1 bg-gradient-to-r from-purple-500/0 via-purple-500/30 to-blue-500/0"></div>

          <div className="flex flex-col items-center justify-center py-8 relative">
            {/* Subtle grid background */}
            <div className="absolute inset-0 bg-[radial-gradient(#2a3a5a_1px,transparent_1px)] bg-[size:20px_20px] opacity-20"></div>

            {/* Icon with glow effect */}
            <div className="relative mb-8">
              <div className="absolute inset-0 bg-blue-500/20 rounded-full blur-xl transform scale-150"></div>
              <div className="bg-[rgba(134,244,156,0.07)] p-6 rounded-full relative z-10 border border-[rgba(134,244,156,0.07)]">
                <Sparkles className="w-10 h-10 text-white" />
              </div>
            </div>

            <h2 className="rexton-light text-lg font-light leading-rexton-small mb-4 text-white text-center">
              {t('createYourFirstProject', 'Create Your First Project')}
            </h2>

            <p className="text-center text-gray-400  mb-8 text-md font-light leading-normal md:whitespace-nowrap">
              {t(
                'startCreateNewProjectDescription',
                'Start building something amazing. Your projects will be displayed here once you create them.',
              )}
            </p>

            {/* Updated button with shimmer effect */}
            <div className="flex justify-center">
              <a
                href="/"
                className="w-full sm:w-auto px-6 py-1.5 bg-gradient-to-r from-green-500 to-green-600 rounded-lg text-white font-light flex items-center justify-center gap-2 group relative overflow-hidden hover:scale-105 transition-transform active:scale-95"
              >
                {/* Shimmer effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -translate-x-full animate-shimmer" />

                {/* Button content */}
                <Plus className="w-5 h-5" />
                <span className="text-md font-light leading-small">{t('createProjectBtn', 'New Project')}</span>
              </a>
            </div>
          </div>
        </div>
      )}

      {/* Modalul de schimbare folder */}
      <AnimatePresence>
        {showChangeFolderModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            key="change-folder-modal"
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-max flex items-center justify-center p-4"
            onClick={() => setShowChangeFolderModal(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-[#0F1526] border border-white/5 rounded-2xl max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <div className={'flex justify-between p-6 border-b border-white/10'}>
                <div className="flex justify-between items-center">
                  <h3 className="text-xl font-medium text-white">{t('selectFolder', 'Choose a Folder')}</h3>
                </div>
                <button onClick={() => setShowChangeFolderModal(false)} className="p-2 rounded-full hover:bg-white/50 bg-transparent transition-colors">
                  <XMarkIcon className="w-5 h-5 text-white/70" />
                </button>
              </div>
              <div className="p-6 space-y-4 relative overflow-visible border-b border-white/10">
                 <div className="relative overflow-visible">
                   <CustomDropdown
                     folders={folders}
                     selectedFolderId={targetFolderId}
                     onChange={(id) => setTargetFolderId(id)}
                   />
                  </div>
              </div>
              <div className="p-6 flex justify-end gap-3">
                <button
                  onClick={() => setShowChangeFolderModal(false)}
                  className="px-4 py-2.5 bg-white/5 text-white/80 rounded-lg hover:bg-white/10 transition-colors font-light disabled:opacity duration-300 relative bg-gradient-to-r to-biela-hover"
                >
                  {t('cancel', 'Cancel')}
                </button>
                <button
                  onClick={handleConfirmChangeFolder}
                  className={`px-4 py-2 bg-[#0bb57c] hover:bg-[#0bb57c]/70 gap-2 flex items-center rounded-lg transition-all duration-300 relative bg-gradient-to-r from-biela-green to-biela-hover text-white font-normal shadow-md hover:shadow-lg hover:from-biela-hover hover:to-biela-green ${
                    !targetFolderId || targetFolderId === currentFolderId || changingFolderProcess
                      ? 'opacity-50 cursor-not-allowed hover-none'
                      : ''
                  }`}
                  disabled={!targetFolderId || targetFolderId === currentFolderId || changingFolderProcess}
                >
                  {changingFolderProcess ? (
                    <FaSpinner className="animate-spin" />
                  ) : (
                    <>
                      <FaCheck />
                      <span>{t('Confirm', 'Confirm')}</span>
                    </>
                  )}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Delete Confirmation Dialog */}
      <AnimatePresence>
        {showDeleteConfirm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            key="delete-confirm-modal"
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowDeleteConfirm(false)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-[#0F1526] border border-white/10 rounded-2xl max-w-md w-full overflow-visible relative overflow-visible"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6 border-b border-white/10 flex justify-between items-center">
                <h3 className="text-xl font-medium text-white">
                  {selectedFolder === 'all' || !selectedFolder
                    ? t('confirmDeleteProject', 'Confirm Delete Project')
                    : t('confirmRemoveFromFolder', 'Confirm Remove from Folder')}
                </h3>
                <button onClick={() => setShowDeleteConfirm(false)} className="p-2 rounded-full hover:bg-white/5 transition-colors bg-transparent">
                  <XMarkIcon className="w-5 h-5 text-white/70" />
                </button>
              </div>
              <div className="p-6">
                <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-4 mb-4">
                  <div className="flex items-start items-center gap-3">
                    <div className="p-2 rounded-lg bg-red/8 flex items-center justify-center flex-shrink-0">
                      <FaExclamationTriangle className="text-red-400 w-4 h-4" />
                    </div>
                    <p className="text-white/90 text-sm font-medium">
                      {selectedFolder === 'all' || !selectedFolder
                        ? t('deleteProjectWarning', {
                            projectName:
                              projects.find((p) => p._id.toString() === projectToDelete?.id)?.projectName ||
                              t('thisProject', 'this project'),
                          })
                        : t('removeFromFolderWarning', {
                            projectName:
                              projects.find((p) => p._id.toString() === projectToDelete?.id)?.projectName ||
                              t('thisProject', 'this project'),
                            folderName: currentFolder,
                          })}
                    </p>
                  </div>
                </div>
                {deleteSuccess ? (
                  <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <FaCheck className="text-green-400" />
                      <p className="text-gray-300">
                        {selectedFolder === 'all' || !selectedFolder
                          ? t('projectDeletedSuccessfully', 'Project deleted successfully')
                          : t('projectRemovedFromFolder', 'Project removed from folder')}
                      </p>
                    </div>
                  </div>
                ) : projectToDelete ? (
                  <div className="bg-[#22D3EE]/6 border border-[#22D3EE]/20 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-[#22D3EE]/8 flex items-center justify-center flex-shrink-0">
                        <FaFolder className="w-4 h-4 text-gray-400 text-xl" />
                      </div>
                      <div>
                        <div className="text-white/90 text-sm font-medium">
                          {projects.find((p) => p._id.toString() === projectToDelete.id)?.projectName ||
                            t('unnamedProject', 'Unnamed Project')}
                        </div>
                        <div className="text-white/60 text-sm font-light">
                          {selectedFolder === 'all' || !selectedFolder
                            ? t('permanentDeletion', 'Permanent deletion')
                            : t('removeFromFolder', 'Remove from folder', { folderName: currentFolder })}
                        </div>
                      </div>
                    </div>
                  </div>
                ) : null}
              </div>
              <div className="p-6 border-t border-white/10 flex justify-end gap-3">
                <button
                  onClick={handleCancelDelete}
                  className={`px-6 py-2.5 bg-white/5 hover:bg-white/10 rounded-lg text-white/80 transition-colors ${deletingInProgress ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={deletingInProgress}
                >
                  {t('cancel', 'Cancel')}
                </button>
                <button
                  onClick={handleConfirmDelete}
                  className={`px-4 py-2 bg-red-500/70 hover:bg-red-700 rounded-lg transition-colors text-white flex items-center gap-2 ${deletingInProgress || deleteSuccess ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={deletingInProgress || deleteSuccess}
                >
                  {deletingInProgress ? (
                    <FaSpinner className="animate-spin" />
                  ) : (
                    <>
                      <FaTrash />
                      <span>{t('confirm', 'Confirm')}</span>
                    </>
                  )}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
        <AnimatePresence>
          {showTransferProjectModal && (
            <TransferModal
              key="transfer-modal"
              isOpen={showTransferProjectModal}
              isLoading={loadingTransferProjects}
              onTransfer={handleTransferProject}
              onClose={handleCloseTransfer}
              errorMessage={transferError}
            />
          )}
        </AnimatePresence>

        <SubmissionModal
          key="submission-modal"
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          onViewContest={handleViewContest}
          onRetry={() => {
            if (modalType === 'publish') {
              handlePublishToContest(currentProjectSlug!);
            } else if (modalType === 'refresh') {
              handleRefreshContestSubmission(currentProjectSlug!);
            }
          }}
          type={modalType}
          state={modalState}
          projectName={currentProjectSlug || 'Project'}
        />
      </AnimatePresence>

      <ConfirmationDialog
        isOpen={isDuplicateOpen}
        type={DialogConfirmationType.DUPLICATE}
        description={projectToDuplicate?.name ?? ''}
        onCancel={() => setIsDuplicateOpen(false)}
        onConfirm={() => handleDuplicateProject(projectToDuplicate?.slug ?? '', projectToDuplicate?.name ?? '')}
        isLoading={isDuplicating}
      />

      <AnimatePresence>
        {showPreview && hoveredProject && (
          <motion.div
            key="project-preview-popup"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="fixed z-[100] bg-[#0F1526] border border-white/10 rounded-lg shadow-2xl flex flex-col"
            style={{
              top:       '20vh',
              left:      `${left}px`,
              width:     `${width}px`,
              maxHeight: '90vh',
              maxWidth: '30%',
              overflowY: 'auto',
            }}
            onMouseEnter={handleMouseEnterPopup}
            onMouseLeave={handleMouseLeavePreviewArea}
            ref={popupRef}
          >
            <div className="flex items-center justify-between p-2 bg-[#1A1F2E] rounded-t-lg">
              <span className="text-sm text-white/70 truncate">{hoveredProject?.projectName}</span>
              <button onClick={handleClosePreviewPopup} className="p-1 bg-transparent" aria-label="Close preview">
                <XMarkIcon className="w-4 h-4 text-white/50" />
              </button>
            </div>
            <div className="flex-1 p-3 overflow-auto flex items-center justify-center min-h-[200px]">
              {hoveredProject.screenshotUrl ? (
                <img
                  src={hoveredProject.screenshotUrl}
                  alt={`${hoveredProject.projectName} preview`}
                  className="block mx-auto rounded-sm"
                  style={{
                    height: '100%',
                    width: '100%',
                    maxHeight: '100%',
                    maxWidth: '100%',
                    objectFit: 'cover'
                  }}
                />
              ) : (
                <div className="text-center text-white/50 text-sm">
                  No screenshot available
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

export default ProjectsListClient;
