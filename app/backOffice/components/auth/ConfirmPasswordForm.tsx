import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { confirmPassword } from '~/ai/lib/stores/user/user';
import { useNavigate } from 'react-router-dom';
import { FaEye, FaEyeSlash, FaLock } from 'react-icons/fa';
import InfoIcon from '~/assets/icons/infoIcon.svg?url';

interface ConfirmPasswordFormProps {
  token: string;
  onSuccessRedirect?: () => void;
}

const ConfirmPasswordForm: React.FC<ConfirmPasswordFormProps> = ({ token, onSuccessRedirect }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({ password: '', confirmPassword: '' });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [passwordFeedback, setPasswordFeedback] = useState('');

  const validatePassword = (password: string) => {
    const regex = /^(?=.*[a-z])(?=.*[A-Z]).{8,}$/;
    return regex.test(password);
  };

  useEffect(() => {
    const password = formData.password;
    let strength = 0;
    let feedback = '';

    if (password.length >= 8) strength++;
    if (/\d/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;

    feedback = strength <= 2 ? 'Weak password' : strength <= 4 ? 'Good password' : 'Strong password';

    setPasswordStrength(strength);
    setPasswordFeedback(feedback);
  }, [formData.password]);

  const getStrengthColor = () => {
    if (passwordStrength <= 2) return 'bg-red-500';
    if (passwordStrength <= 4) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    if (!validatePassword(formData.password)) {
      setError(t('PasswordRequirements', 'Password must be at least 8 characters and include upper/lowercase letters'));
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setError(t('PasswordsMismatch', 'Passwords do not match.'));
      return;
    }

    try {
      await confirmPassword({ token, password: formData.password });
      setSuccess(t('PasswordUpdatedSuccess', 'Password updated successfully!'));
      setTimeout(() => {
        if (onSuccessRedirect) {
          onSuccessRedirect();
        }
      }, 1000);
    } catch (err: any) {
      setError(err.message || t('SomethingWentWrong', 'Something went wrong'));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="flex flex-col gap-4 px-2 py-2 w-full">
      <div className="relative">
        <input
          type={showPassword ? 'text' : 'password'}
          name="password"
          value={formData.password}
          onChange={handleChange}
          placeholder={t('NewPasswordPlaceholder', 'New password')}
          className="w-full bg-white/10 backdrop-blur-sm border rounded-lg pl-12 pr-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 border-white/20"
        />
        <div className="absolute left-4 top-0 bottom-0 flex items-center justify-center">
          <FaLock className="text-gray-400" />
        </div>
        <div
          onClick={() => setShowPassword(!showPassword)}
          className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer text-white/50 hover:text-white"
        >
          {showPassword ? <FaEyeSlash /> : <FaEye />}
        </div>
      </div>

      {formData.password && (
        <div>
          <div className="flex justify-between items-center mb-2">
            <span
              className={`text-xs font-light ${
                passwordStrength <= 2 ? 'text-red-400' : passwordStrength <= 4 ? 'text-yellow-400' : 'text-green-400'
              }`}
            >
              {passwordFeedback}
            </span>
            <span className="text-xs text-gray-400 font-light">{passwordStrength}/5</span>
          </div>
          <div className="h-1 w-full bg-[#374151] rounded-full overflow-hidden">
            <div
              className={`h-full ${getStrengthColor()} transition-all duration-300`}
              style={{ width: `${(passwordStrength / 5) * 100}%` }}
            ></div>
          </div>
        </div>
      )}

      <div className="relative">
        <input
          type={showPassword ? 'text' : 'password'}
          name="confirmPassword"
          value={formData.confirmPassword}
          onChange={handleChange}
          placeholder={t('ConfirmNewPasswordPlaceholder', 'Confirm new password')}
          className="w-full bg-white/10 backdrop-blur-sm border rounded-lg pl-12 pr-4 py-3 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-green-500 border-white/20"
        />
        <div className="absolute left-4 top-0 bottom-0 flex items-center justify-center">
          <FaLock className="text-gray-400" />
        </div>
        <div
          onClick={() => setShowPassword(!showPassword)}
          className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer text-white/50 hover:text-white"
        >
          {showPassword ? <FaEyeSlash /> : <FaEye />}
        </div>
      </div>

      <button
        type="submit"
        className="mt-2 w-full bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg transition-colors font-light flex items-center justify-center"
      >
        {t('ResetPasswordButton', 'Reset Password')}
      </button>

      {error && (
        <div className="flex items-start rounded-lg p-3 bg-[rgba(134,244,156,0.07)] text-red-400 text-sm">
          <img src={InfoIcon} alt="info" className="mr-2 w-5 h-5" />
          <p>{error}</p>
        </div>
      )}

      {success && (
        <div className="flex items-start rounded-lg p-3 bg-[rgba(134,244,156,0.07)] text-green-400 text-sm">
          <img src={InfoIcon} alt="info" className="mr-2 w-5 h-5" />
          <p>{success}</p>
        </div>
      )}
    </form>
  );
};

export default ConfirmPasswordForm;
