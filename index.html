<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>biela.dev</title>

    <!-- 1) Hide launcher + iframe on “forbidden” paths -->
    <style>
      html.hide-zendesk #launcher,
      html.hide-zendesk iframe[title="Messaging window"],
      html.hide-zendesk iframe[title="Fereastra pentru mesagerie"],
      html.hide-zendesk iframe[name="Fereastra pentru mesagerie"],
      html.hide-zendesk iframe[name="Messaging window"] {
        display: none !important;
      }
    </style>

    <!-- 2) Early script to toggle .hide-zendesk and patch SPA routing -->
    <script>
      (function() {
        function update(path) {
          const hide = path === '/' || path === '/chat' || path.startsWith('/chat/');
          document.documentElement.classList.toggle('hide-zendesk', hide);
          sessionStorage.setItem('ZD-widgetOpen', 'false');
        }
        // Initial check
        update(location.pathname);

        // Patch history APIs so SPA navigations re‐check
        const _push = history.pushState;
        const _replace = history.replaceState;
        history.pushState = function(...args) {
          _push.apply(this, args);
          update(location.pathname);
        };
        history.replaceState = function(...args) {
          _replace.apply(this, args);
          update(location.pathname);
        };
        window.addEventListener('popstate', () => update(location.pathname));
      })();
    </script>

    <!-- 3) Load the Zendesk snippet after our “hide” logic has run -->
    <script
      id="ze-snippet"
      src="https://static.zdassets.com/ekr/snippet.js?key=9e8ddf01-c97a-41dd-8605-91c06f5fbe2f"
      async
      defer
    ></script>

    <link rel="stylesheet" href="/css/ReactToastify.css" />
    <link rel="stylesheet" href="/css/tailwind-compat.css" />
    <link rel="stylesheet" href="/css/xterm.css" />
    <link rel="stylesheet" href="/app/components/styles/index.scss" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous" />
    <link rel="icon" type="image/svg+xml" href="/biela_favicon_light.svg" media="(prefers-color-scheme: light)" />
    <link rel="icon" type="image/svg+xml" href="/biela_favicon_dark.svg" media="(prefers-color-scheme: dark)" />


    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
    />
    <!-- Global site tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-CSZ44DYPWS"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag('js', new Date());
    </script>
    <script>
      function setTutorialKitTheme() {
        let theme = localStorage.getItem('biela_theme') ?? localStorage.getItem('data-theme');
        if (!theme) {
          theme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'dark';
        }
        document.querySelector('html')?.setAttribute('data-theme', theme);
      }
      setTutorialKitTheme();
    </script>

    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
  </head>
<body>
<div id="root"></div>
<script type="module" src="/app/root.tsx"></script>
</body>
</html>
